/**
 * 员工管理相关 API
 */

import { get, post, put } from './request';
import { ApiError } from './types';

// 员工数据类型定义
export interface Staff {
  uid: string;
  name: string;
  gender: 'MALE' | 'FEMALE';
  pwd_status: boolean;
  phone: string;
  email: string;
  department: string;
  position: string;
  region: string;
  join_date: string;
  contract_end_date: string;
  last_login_time: string | null;
  remark: string;
  created_at: string;
  updated_at: string;
}

// 员工列表显示数据类型
export interface StaffListItem {
  uid: string;
  name: string;
  gender: 'MALE' | 'FEMALE';
  phone: string;
  department: string;
  position: string;
  region: string;
  join_date: string;
  remark: string;
}

// 分页响应数据类型
export interface StaffListResponse {
  list: StaffListItem[];
  page: number;
  page_size: number;
  total_count: number;
  total_page: number;
}

// 创建/更新员工请求数据类型
export interface CreateStaffRequest {
  name: string;
  gender: 'MALE' | 'FEMALE';
  phone: string;
  email: string;
  department: string;
  position: string;
  region: string;
  join_date: string;
  contract_end_date: string;
  remark: string;
}

export interface UpdateStaffRequest extends CreateStaffRequest {
  // 更新请求与创建请求相同，密码由后端自动生成
}

/**
 * 员工管理 API 类
 */
export class StaffApi {
  /**
   * 获取员工列表（分页）
   */
  static async getStaffList(
    page = 1,
    pageSize = 10,
    search?: string,
    filters?: {
      department?: string;
      position?: string;
      region?: string;
      gender?: string;
    }
  ): Promise<StaffListResponse> {
    try {
      const params: any = {
        page,
        page_size: pageSize,
      };

      if (search && search.trim()) {
        params.search = search.trim();
      }

      // 添加筛选参数
      if (filters) {
        if (filters.department) {
          params.department = filters.department;
        }
        if (filters.position) {
          params.position = filters.position;
        }
        if (filters.region) {
          params.region = filters.region;
        }
        if (filters.gender) {
          params.gender = filters.gender;
        }
      }

      return await get<StaffListResponse>('/staff/list/', params);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`获取员工列表失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 获取员工详情
   */
  static async getStaffDetail(uid: string): Promise<Staff> {
    try {
      return await get<Staff>(`/staff/detail/${uid}/`);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`获取员工详情失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 创建员工
   */
  static async createStaff(staffData: CreateStaffRequest): Promise<Staff> {
    try {
      return await post<Staff>('/staff/create/', staffData);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`创建员工失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 更新员工信息
   */
  static async updateStaff(uid: string, staffData: UpdateStaffRequest): Promise<Staff> {
    try {
      return await put<Staff>(`/staff/update/${uid}/`, staffData);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`更新员工信息失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 员工离职
   */
  static async staffLeave(uid: string): Promise<void> {
    try {
      await post<null>(`/staff/leave/${uid}/`);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`员工离职操作失败: ${error.msg}`);
      }
      throw error;
    }
  }
}

/**
 * React Hook 形式的员工 API
 */
export const useStaffApi = () => {
  const getStaffList = async (
    page = 1,
    pageSize = 10,
    search?: string,
    filters?: {
      department?: string;
      position?: string;
      region?: string;
      gender?: string;
    }
  ) => {
    try {
      const result = await StaffApi.getStaffList(page, pageSize, search, filters);
      return result;
    } catch (error) {
      console.error('获取员工列表失败:', error);
      throw error;
    }
  };

  const getStaffDetail = async (uid: string) => {
    try {
      const result = await StaffApi.getStaffDetail(uid);
      return result;
    } catch (error) {
      console.error('获取员工详情失败:', error);
      throw error;
    }
  };

  const createStaff = async (staffData: CreateStaffRequest) => {
    try {
      const result = await StaffApi.createStaff(staffData);
      return result;
    } catch (error) {
      console.error('创建员工失败:', error);
      throw error;
    }
  };

  const updateStaff = async (uid: string, staffData: UpdateStaffRequest) => {
    try {
      const result = await StaffApi.updateStaff(uid, staffData);
      return result;
    } catch (error) {
      console.error('更新员工失败:', error);
      throw error;
    }
  };

  const staffLeave = async (uid: string) => {
    try {
      await StaffApi.staffLeave(uid);
    } catch (error) {
      console.error('员工离职操作失败:', error);
      throw error;
    }
  };

  return {
    getStaffList,
    getStaffDetail,
    createStaff,
    updateStaff,
    staffLeave,
  };
};
