"use client"

import { useState, useCallback } from 'react'
import { UniversalFilter, FilterConfig, FilterValues } from './UniversalFilter'
import { PlusCircle } from 'lucide-react'

// 简单的测试配置
const testConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索内容...',
    searchable: true
  },
  {
    key: 'status',
    label: '状态',
    type: 'enum',
    options: [
      { value: 'active', label: '激活' },
      { value: 'inactive', label: '非激活' }
    ]
  },
  {
    key: 'date',
    label: '日期',
    type: 'date'
  }
]

export function SimpleFilterTest() {
  const [filters, setFilters] = useState<FilterValues>({})
  const [renderCount, setRenderCount] = useState(0)

  // 使用 useCallback 确保函数引用稳定
  const handleFiltersChange = useCallback((newFilters: FilterValues) => {
    console.log('筛选条件变化:', newFilters)
    setFilters(newFilters)
  }, [])

  const handleAddClick = useCallback(() => {
    console.log('点击添加按钮')
  }, [])

  // 监控渲染次数
  useState(() => {
    setRenderCount(prev => prev + 1)
  })

  return (
    <div className="space-y-6 p-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-2">简单筛选测试</h2>
        <p className="text-sm text-gray-600 mb-2">
          渲染次数: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{renderCount}</span>
        </p>
        <p className="text-sm text-gray-600">
          如果渲染次数不断增加，说明存在无限循环问题。
        </p>
      </div>

      <UniversalFilter
        configs={testConfigs}
        onFiltersChange={handleFiltersChange}
        onActionClick={handleAddClick}
        actionLabel="添加项目"
        actionIcon={<PlusCircle className="mr-2 h-4 w-4" />}
        className="border rounded-lg p-4 bg-white"
      />

      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">当前筛选结果:</h3>
        <pre className="text-sm text-gray-600 whitespace-pre-wrap">
          {JSON.stringify(filters, null, 2)}
        </pre>
      </div>
    </div>
  )
}
