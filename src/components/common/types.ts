// 通用筛选组件的类型定义

/**
 * 筛选器类型
 */
export type FilterType = 'enum' | 'text' | 'date' | 'dateRange'

/**
 * 筛选选项
 */
export interface FilterOption {
  readonly value: string
  readonly label: string
}

/**
 * 筛选器配置
 */
export interface FilterConfig {
  /** 筛选器唯一标识 */
  key: string
  /** 筛选器显示标签 */
  label: string
  /** 筛选器类型 */
  type: FilterType
  /** 占位符文本 */
  placeholder?: string
  /** 枚举类型的选项（仅当 type 为 'enum' 时使用） */
  options?: readonly FilterOption[]
  /** 是否在搜索栏中显示（仅一个配置项可设置为 true） */
  searchable?: boolean
}

/**
 * 筛选值
 */
export interface FilterValues {
  [key: string]: any
}

/**
 * 日期范围值
 */
export interface DateRangeValue {
  start?: string
  end?: string
}

/**
 * 通用筛选组件属性
 */
export interface UniversalFilterProps {
  /** 筛选器配置数组 */
  configs: FilterConfig[]
  /** 筛选条件变化回调 */
  onFiltersChange: (filters: FilterValues) => void
  /** 操作按钮点击回调 */
  onActionClick?: () => void
  /** 操作按钮文本 */
  actionLabel?: string
  /** 操作按钮图标 */
  actionIcon?: React.ReactNode
  /** 自定义样式类 */
  className?: string
  /** 搜索框占位符 */
  searchPlaceholder?: string
}

/**
 * 预定义的常用筛选配置模板
 */
export const CommonFilterTemplates = {
  /**
   * 搜索筛选器
   */
  search: (key: string, label: string = '搜索', placeholder?: string): FilterConfig => ({
    key,
    label,
    type: 'text',
    placeholder: placeholder || `${label}...`,
    searchable: true
  }),

  /**
   * 状态筛选器
   */
  status: (key: string, options: FilterOption[], label: string = '状态'): FilterConfig => ({
    key,
    label,
    type: 'enum',
    placeholder: `选择${label}`,
    options
  }),

  /**
   * 日期筛选器
   */
  date: (key: string, label: string, placeholder?: string): FilterConfig => ({
    key,
    label,
    type: 'date',
    placeholder: placeholder || `选择${label}`
  }),

  /**
   * 日期范围筛选器
   */
  dateRange: (key: string, label: string): FilterConfig => ({
    key,
    label,
    type: 'dateRange'
  }),

  /**
   * 文本输入筛选器
   */
  text: (key: string, label: string, placeholder?: string): FilterConfig => ({
    key,
    label,
    type: 'text',
    placeholder: placeholder || `输入${label}`
  })
}

/**
 * 常用状态选项
 */
export const CommonStatusOptions = {
  /** 通用启用/禁用状态 */
  enabledDisabled: [
    { value: 'enabled', label: '启用' },
    { value: 'disabled', label: '禁用' }
  ],

  /** 通用激活/非激活状态 */
  activeInactive: [
    { value: 'active', label: '激活' },
    { value: 'inactive', label: '非激活' }
  ],

  /** 项目状态 */
  projectStatus: [
    { value: 'planning', label: '规划中' },
    { value: 'development', label: '开发中' },
    { value: 'testing', label: '测试中' },
    { value: 'completed', label: '已完成' },
    { value: 'cancelled', label: '已取消' }
  ],

  /** 订单状态 */
  orderStatus: [
    { value: 'pending', label: '待处理' },
    { value: 'processing', label: '处理中' },
    { value: 'shipped', label: '已发货' },
    { value: 'delivered', label: '已送达' },
    { value: 'cancelled', label: '已取消' }
  ],

  /** 优先级 */
  priority: [
    { value: 'high', label: '高' },
    { value: 'medium', label: '中' },
    { value: 'low', label: '低' }
  ],

  /** 性别 */
  gender: [
    { value: 'male', label: '男' },
    { value: 'female', label: '女' }
  ]
}

/**
 * 筛选值工具函数
 */
export const FilterUtils = {
  /**
   * 检查筛选值是否为空
   */
  isEmpty: (filters: FilterValues): boolean => {
    return Object.keys(filters).length === 0
  },

  /**
   * 获取激活的筛选器数量
   */
  getActiveCount: (filters: FilterValues): number => {
    return Object.keys(filters).length
  },

  /**
   * 清除指定的筛选器
   */
  removeFilter: (filters: FilterValues, key: string): FilterValues => {
    const newFilters = { ...filters }
    delete newFilters[key]
    return newFilters
  },

  /**
   * 清除所有筛选器
   */
  clearAll: (): FilterValues => {
    return {}
  },

  /**
   * 合并筛选器
   */
  merge: (filters1: FilterValues, filters2: FilterValues): FilterValues => {
    return { ...filters1, ...filters2 }
  },

  /**
   * 格式化日期范围值
   */
  formatDateRange: (value: DateRangeValue): string => {
    const { start, end } = value
    if (start && end) {
      return `${start} ~ ${end}`
    } else if (start) {
      return `从 ${start}`
    } else if (end) {
      return `到 ${end}`
    }
    return ''
  }
}
