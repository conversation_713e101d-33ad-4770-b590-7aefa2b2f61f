"use client"

import { useState, useCallback } from 'react'
import { UniversalFilter, FilterConfig, FilterValues } from './UniversalFilter'
import { QuickDateRangePicker } from './DatePicker'
import { PlusCircle, Calendar, Users, FileText } from 'lucide-react'

// 增强版示例配置
const enhancedConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索项目名称、编号...',
    searchable: true
  },
  {
    key: 'status',
    label: '项目状态',
    type: 'enum',
    options: [
      { value: 'planning', label: '📋 规划中' },
      { value: 'development', label: '🔧 开发中' },
      { value: 'testing', label: '🧪 测试中' },
      { value: 'completed', label: '✅ 已完成' },
      { value: 'cancelled', label: '❌ 已取消' }
    ]
  },
  {
    key: 'priority',
    label: '优先级',
    type: 'enum',
    options: [
      { value: 'high', label: '🔴 高' },
      { value: 'medium', label: '🟡 中' },
      { value: 'low', label: '🟢 低' }
    ]
  },
  {
    key: 'startDate',
    label: '开始日期',
    type: 'date',
    placeholder: '选择项目开始日期'
  },
  {
    key: 'timeline',
    label: '项目周期',
    type: 'dateRange'
  },
  {
    key: 'team',
    label: '团队',
    type: 'enum',
    options: [
      { value: 'frontend', label: '前端团队' },
      { value: 'backend', label: '后端团队' },
      { value: 'mobile', label: '移动端团队' },
      { value: 'design', label: '设计团队' },
      { value: 'qa', label: '测试团队' }
    ]
  }
]

export function EnhancedFilterExample() {
  const [filters, setFilters] = useState<FilterValues>({})
  const [quickDateRange, setQuickDateRange] = useState<{ start?: string; end?: string } | undefined>(undefined)

  const handleFiltersChange = useCallback((newFilters: FilterValues) => {
    console.log('筛选条件变化:', newFilters)
    setFilters(newFilters)
  }, [])

  const handleAddClick = useCallback(() => {
    console.log('点击新建项目')
  }, [])

  // 模拟的项目数据
  const mockProjects = [
    { id: 1, name: '电商平台重构', status: 'development', priority: 'high', team: 'frontend' },
    { id: 2, name: '移动端APP', status: 'testing', priority: 'medium', team: 'mobile' },
    { id: 3, name: '数据分析系统', status: 'planning', priority: 'low', team: 'backend' },
    { id: 4, name: 'UI设计系统', status: 'completed', priority: 'high', team: 'design' },
    { id: 5, name: '自动化测试', status: 'development', priority: 'medium', team: 'qa' }
  ]

  // 根据筛选条件过滤项目
  const filteredProjects = mockProjects.filter(project => {
    if (filters.search && !project.name.toLowerCase().includes(filters.search.toLowerCase())) {
      return false
    }
    if (filters.status && project.status !== filters.status) {
      return false
    }
    if (filters.priority && project.priority !== filters.priority) {
      return false
    }
    if (filters.team && project.team !== filters.team) {
      return false
    }
    return true
  })

  const getStatusColor = (status: string) => {
    const colors = {
      planning: 'bg-blue-100 text-blue-800 border-blue-200',
      development: 'bg-orange-100 text-orange-800 border-orange-200',
      testing: 'bg-purple-100 text-purple-800 border-purple-200',
      completed: 'bg-green-100 text-green-800 border-green-200',
      cancelled: 'bg-red-100 text-red-800 border-red-200'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    }
    return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <FileText className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-bold">增强版筛选组件示例</h1>
        </div>
        <p className="text-gray-600">
          展示优化后的日期选择器和更丰富的筛选功能
        </p>
      </div>

      {/* 主要筛选组件 */}
      <UniversalFilter
        configs={enhancedConfigs}
        onFiltersChange={handleFiltersChange}
        onActionClick={handleAddClick}
        actionLabel="新建项目"
        actionIcon={<PlusCircle className="mr-2 h-4 w-4" />}
        className="border rounded-lg p-4 bg-white shadow-sm"
      />

      {/* 快速日期范围选择器示例 */}
      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <div className="flex items-center gap-2 mb-3">
          <Calendar className="h-4 w-4 text-blue-600" />
          <h3 className="text-lg font-semibold">快速日期范围选择器</h3>
        </div>
        <QuickDateRangePicker
          value={quickDateRange}
          onChange={setQuickDateRange}
        />
        {quickDateRange && (quickDateRange.start || quickDateRange.end) ? (
          <div className="mt-3 p-2 bg-blue-50 rounded text-sm text-blue-700">
            选择的日期范围: {quickDateRange.start || '未设置'} ~ {quickDateRange.end || '未设置'}
          </div>
        ) : null}
      </div>

      {/* 筛选结果展示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 项目列表 */}
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <div className="flex items-center gap-2 mb-4">
            <Users className="h-4 w-4 text-green-600" />
            <h3 className="text-lg font-semibold">
              项目列表 ({filteredProjects.length}/{mockProjects.length})
            </h3>
          </div>
          <div className="space-y-3">
            {filteredProjects.map(project => (
              <div key={project.id} className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{project.name}</h4>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(project.priority)}`}>
                    {project.priority}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className={`px-2 py-1 rounded border text-xs ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                  <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                    {project.team}
                  </span>
                </div>
              </div>
            ))}
            {filteredProjects.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>没有找到匹配的项目</p>
              </div>
            )}
          </div>
        </div>

        {/* 筛选条件详情 */}
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <h3 className="text-lg font-semibold mb-4">当前筛选条件</h3>
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">主要筛选器</h4>
              <pre className="text-xs text-gray-600 bg-gray-50 p-3 rounded whitespace-pre-wrap">
                {JSON.stringify(filters, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">快速日期范围</h4>
              <pre className="text-xs text-gray-600 bg-gray-50 p-3 rounded whitespace-pre-wrap">
                {JSON.stringify(quickDateRange || {}, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>

      {/* 功能说明 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2 text-blue-800">✨ 新功能亮点</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>优化的日历样式</strong>: 更清晰的布局和更好的视觉效果</li>
          <li>• <strong>智能日期选择器</strong>: 支持清除按钮和中文日期格式</li>
          <li>• <strong>快速日期范围</strong>: 提供常用的日期范围快捷选项</li>
          <li>• <strong>增强的用户体验</strong>: 更流畅的交互和更直观的界面</li>
          <li>• <strong>实时筛选预览</strong>: 即时查看筛选结果</li>
        </ul>
      </div>
    </div>
  )
}
