"use client"

import { useState, useCallback } from 'react'
import { UniversalFilter, FilterConfig, FilterValues } from './UniversalFilter'
import { PlusCircle } from 'lucide-react'

// 示例：员工管理筛选配置
const staffFilterConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索员工姓名、工号...',
    searchable: true // 这个会显示在搜索栏
  },
  {
    key: 'department',
    label: '部门',
    type: 'enum',
    placeholder: '选择部门',
    options: [
      { value: 'tech', label: '技术部' },
      { value: 'sales', label: '销售部' },
      { value: 'hr', label: '人事部' },
      { value: 'finance', label: '财务部' }
    ]
  },
  {
    key: 'position',
    label: '职位',
    type: 'enum',
    placeholder: '选择职位',
    options: [
      { value: 'manager', label: '经理' },
      { value: 'senior', label: '高级工程师' },
      { value: 'junior', label: '初级工程师' },
      { value: 'intern', label: '实习生' }
    ]
  },
  {
    key: 'joinDate',
    label: '入职日期',
    type: 'date',
    placeholder: '选择入职日期'
  },
  {
    key: 'workPeriod',
    label: '工作时间段',
    type: 'dateRange',
    placeholder: '选择工作时间段'
  }
]

// 示例：项目管理筛选配置
const projectFilterConfigs: FilterConfig[] = [
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '搜索项目名称...',
    searchable: true
  },
  {
    key: 'status',
    label: '项目状态',
    type: 'enum',
    options: [
      { value: 'planning', label: '规划中' },
      { value: 'development', label: '开发中' },
      { value: 'testing', label: '测试中' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' }
    ]
  },
  {
    key: 'priority',
    label: '优先级',
    type: 'enum',
    options: [
      { value: 'high', label: '高' },
      { value: 'medium', label: '中' },
      { value: 'low', label: '低' }
    ]
  },
  {
    key: 'startDate',
    label: '开始日期',
    type: 'date'
  },
  {
    key: 'timeline',
    label: '项目周期',
    type: 'dateRange'
  },
  {
    key: 'budget',
    label: '预算范围',
    type: 'text',
    placeholder: '输入预算范围'
  }
]

// 示例：订单管理筛选配置
const orderFilterConfigs: FilterConfig[] = [
  {
    key: 'orderNumber',
    label: '订单号',
    type: 'text',
    placeholder: '搜索订单号...',
    searchable: true
  },
  {
    key: 'orderStatus',
    label: '订单状态',
    type: 'enum',
    options: [
      { value: 'pending', label: '待处理' },
      { value: 'processing', label: '处理中' },
      { value: 'shipped', label: '已发货' },
      { value: 'delivered', label: '已送达' },
      { value: 'cancelled', label: '已取消' }
    ]
  },
  {
    key: 'paymentMethod',
    label: '支付方式',
    type: 'enum',
    options: [
      { value: 'credit_card', label: '信用卡' },
      { value: 'alipay', label: '支付宝' },
      { value: 'wechat', label: '微信支付' },
      { value: 'bank_transfer', label: '银行转账' }
    ]
  },
  {
    key: 'orderDate',
    label: '下单日期',
    type: 'date'
  },
  {
    key: 'deliveryPeriod',
    label: '配送时间段',
    type: 'dateRange'
  },
  {
    key: 'customerName',
    label: '客户姓名',
    type: 'text',
    placeholder: '输入客户姓名'
  }
]

export function UniversalFilterExample() {
  const [currentExample, setCurrentExample] = useState<'staff' | 'project' | 'order'>('staff')
  const [filters, setFilters] = useState<FilterValues>({})

  const handleFiltersChange = useCallback((newFilters: FilterValues) => {
    setFilters(newFilters)
    console.log('筛选条件变化:', newFilters)
  }, [])

  const handleAddClick = useCallback(() => {
    console.log('点击添加按钮')
  }, [])

  const getCurrentConfig = () => {
    switch (currentExample) {
      case 'staff':
        return staffFilterConfigs
      case 'project':
        return projectFilterConfigs
      case 'order':
        return orderFilterConfigs
      default:
        return staffFilterConfigs
    }
  }

  const getCurrentActionLabel = () => {
    switch (currentExample) {
      case 'staff':
        return '添加员工'
      case 'project':
        return '新建项目'
      case 'order':
        return '创建订单'
      default:
        return '添加'
    }
  }

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">通用筛选组件示例</h1>
        
        {/* 示例切换按钮 */}
        <div className="flex gap-2">
          <button
            onClick={() => setCurrentExample('staff')}
            className={`px-4 py-2 rounded ${
              currentExample === 'staff' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            员工管理
          </button>
          <button
            onClick={() => setCurrentExample('project')}
            className={`px-4 py-2 rounded ${
              currentExample === 'project' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            项目管理
          </button>
          <button
            onClick={() => setCurrentExample('order')}
            className={`px-4 py-2 rounded ${
              currentExample === 'order' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            订单管理
          </button>
        </div>
      </div>

      {/* 通用筛选组件 */}
      <UniversalFilter
        configs={getCurrentConfig()}
        onFiltersChange={handleFiltersChange}
        onActionClick={handleAddClick}
        actionLabel={getCurrentActionLabel()}
        actionIcon={<PlusCircle className="mr-2 h-4 w-4" />}
        className="border rounded-lg p-4 bg-white"
      />

      {/* 当前筛选结果显示 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">当前筛选结果:</h3>
        <pre className="text-sm text-gray-600 whitespace-pre-wrap">
          {JSON.stringify(filters, null, 2)}
        </pre>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2 text-blue-800">使用说明:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>枚举筛选</strong>: 支持下拉选择，如部门、状态等</li>
          <li>• <strong>文字筛选</strong>: 支持文本输入，可设置为搜索栏显示</li>
          <li>• <strong>单个时间筛选</strong>: 支持日期选择器</li>
          <li>• <strong>时间段筛选</strong>: 支持开始和结束日期选择</li>
          <li>• <strong>搜索功能</strong>: 设置 searchable: true 的字段会显示在搜索栏</li>
          <li>• <strong>筛选标签</strong>: 已选择的筛选条件会显示为可删除的标签</li>
          <li>• <strong>清除功能</strong>: 支持一键清除所有筛选条件</li>
        </ul>
      </div>
    </div>
  )
}
