# 通用筛选组件 (UniversalFilter)

一个功能强大、高度可配置的通用筛选组件，支持多种筛选类型，样式参考 StaffFilter.tsx。

## 功能特性

- ✅ **枚举筛选**: 下拉选择框，支持预定义选项
- ✅ **文字筛选**: 文本输入框，支持搜索栏显示
- ✅ **单个时间筛选**: 日期选择器
- ✅ **时间段筛选**: 开始和结束日期选择
- ✅ **筛选标签**: 显示已选择的筛选条件
- ✅ **一键清除**: 清除所有筛选条件
- ✅ **响应式设计**: 适配移动端和桌面端
- ✅ **TypeScript 支持**: 完整的类型定义

## 快速开始

### 1. 基本导入

```typescript
import { UniversalFilter, FilterConfig, FilterValues } from '@/components/common/UniversalFilter'
```

### 2. 定义筛选配置

```typescript
const filterConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索员工姓名、工号...',
    searchable: true // 显示在搜索栏
  },
  {
    key: 'department',
    label: '部门',
    type: 'enum',
    placeholder: '选择部门',
    options: [
      { value: 'tech', label: '技术部' },
      { value: 'sales', label: '销售部' },
      { value: 'hr', label: '人事部' }
    ]
  },
  {
    key: 'joinDate',
    label: '入职日期',
    type: 'date',
    placeholder: '选择入职日期'
  },
  {
    key: 'workPeriod',
    label: '工作时间段',
    type: 'dateRange'
  }
]
```

### 3. 使用组件

```typescript
function MyComponent() {
  const [filters, setFilters] = useState<FilterValues>({})

  const handleFiltersChange = (newFilters: FilterValues) => {
    setFilters(newFilters)
    // 处理筛选逻辑
  }

  const handleAddClick = () => {
    // 处理添加按钮点击
  }

  return (
    <UniversalFilter
      configs={filterConfigs}
      onFiltersChange={handleFiltersChange}
      onActionClick={handleAddClick}
      actionLabel="添加员工"
      actionIcon={<PlusCircle className="mr-2 h-4 w-4" />}
    />
  )
}
```

## API 参考

### UniversalFilter Props

| 属性 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| `configs` | `FilterConfig[]` | ✅ | - | 筛选器配置数组 |
| `onFiltersChange` | `(filters: FilterValues) => void` | ✅ | - | 筛选条件变化回调 |
| `onActionClick` | `() => void` | ❌ | - | 操作按钮点击回调 |
| `actionLabel` | `string` | ❌ | - | 操作按钮文本 |
| `actionIcon` | `React.ReactNode` | ❌ | - | 操作按钮图标 |
| `className` | `string` | ❌ | - | 自定义样式类 |
| `searchPlaceholder` | `string` | ❌ | "搜索..." | 搜索框占位符 |

### FilterConfig

| 属性 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `key` | `string` | ✅ | 筛选器唯一标识 |
| `label` | `string` | ✅ | 筛选器显示标签 |
| `type` | `FilterType` | ✅ | 筛选器类型 |
| `placeholder` | `string` | ❌ | 占位符文本 |
| `options` | `FilterOption[]` | ❌ | 枚举类型的选项 |
| `searchable` | `boolean` | ❌ | 是否在搜索栏显示 |

### FilterType

```typescript
type FilterType = 'enum' | 'text' | 'date' | 'dateRange'
```

- `enum`: 枚举选择（下拉框）
- `text`: 文本输入
- `date`: 单个日期选择
- `dateRange`: 日期范围选择

### FilterOption

```typescript
interface FilterOption {
  value: string
  label: string
}
```

### FilterValues

```typescript
interface FilterValues {
  [key: string]: any
}
```

筛选值的数据结构：
- 文本类型: `string`
- 枚举类型: `string`
- 日期类型: `string` (格式: "yyyy-MM-dd")
- 日期范围类型: `{ start?: string, end?: string }`

## 使用示例

### 员工管理筛选

```typescript
const staffConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索员工姓名、工号...',
    searchable: true
  },
  {
    key: 'department',
    label: '部门',
    type: 'enum',
    options: [
      { value: 'tech', label: '技术部' },
      { value: 'sales', label: '销售部' }
    ]
  },
  {
    key: 'joinDate',
    label: '入职日期',
    type: 'date'
  }
]
```

### 项目管理筛选

```typescript
const projectConfigs: FilterConfig[] = [
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    searchable: true
  },
  {
    key: 'status',
    label: '项目状态',
    type: 'enum',
    options: [
      { value: 'planning', label: '规划中' },
      { value: 'development', label: '开发中' },
      { value: 'completed', label: '已完成' }
    ]
  },
  {
    key: 'timeline',
    label: '项目周期',
    type: 'dateRange'
  }
]
```

## 样式定制

组件使用 Tailwind CSS 和 shadcn/ui 组件库，可以通过 `className` 属性进行样式定制：

```typescript
<UniversalFilter
  configs={configs}
  onFiltersChange={handleFiltersChange}
  className="border-2 border-blue-200 rounded-xl p-6 bg-blue-50"
/>
```

## 注意事项

1. **搜索功能**: 只有一个配置项可以设置 `searchable: true`
2. **日期格式**: 日期值统一使用 "yyyy-MM-dd" 格式
3. **日期范围**: 返回格式为 `{ start?: string, end?: string }`
4. **性能优化**: 组件内部使用 `useCallback` 优化性能
5. **类型安全**: 建议使用 TypeScript 以获得更好的开发体验

## 依赖项

- React 18+
- date-fns
- lucide-react
- @/components/ui/* (shadcn/ui 组件)
- @/lib/utils (cn 函数)

## 文件结构

```
src/components/common/
├── UniversalFilter.tsx          # 主要的通用筛选组件
├── DatePicker.tsx               # 优化的日期选择器组件
├── UniversalFilterExample.tsx   # 基础使用示例组件
├── EnhancedFilterExample.tsx    # 增强版示例组件
├── SimpleFilterTest.tsx         # 简单测试组件
├── StaffFilterReplacement.tsx   # 员工筛选的替换组件
├── types.ts                     # 类型定义和工具函数
└── README.md                    # 使用文档
```

## 快速替换现有筛选器

如果您想要替换现有的 StaffFilter 组件，可以直接使用 `StaffFilterReplacement`：

```typescript
// 原来的代码
import { StaffFilter } from '@/components/staff-management/StaffFilter'

// 替换为
import { StaffFilterReplacement as StaffFilter } from '@/components/common/StaffFilterReplacement'

// 使用方式完全相同，无需修改其他代码
<StaffFilter
  onFiltersChange={handleFiltersChange}
  onAddClick={handleAddClick}
  className="my-custom-class"
/>
```

## 高级用法

### 使用预定义模板

```typescript
import { CommonFilterTemplates, CommonStatusOptions } from '@/components/common/types'

const configs: FilterConfig[] = [
  CommonFilterTemplates.search('search', '搜索', '搜索用户...'),
  CommonFilterTemplates.status('status', CommonStatusOptions.projectStatus),
  CommonFilterTemplates.dateRange('timeline', '项目周期'),
  CommonFilterTemplates.text('budget', '预算', '输入预算范围')
]
```

### 自定义筛选值处理

```typescript
import { FilterUtils } from '@/components/common/types'

const handleFiltersChange = (filters: FilterValues) => {
  // 检查是否有激活的筛选器
  if (FilterUtils.isEmpty(filters)) {
    console.log('没有激活的筛选器')
    return
  }

  // 获取激活的筛选器数量
  const count = FilterUtils.getActiveCount(filters)
  console.log(`有 ${count} 个激活的筛选器`)

  // 处理日期范围
  if (filters.dateRange) {
    const rangeText = FilterUtils.formatDateRange(filters.dateRange)
    console.log('日期范围:', rangeText)
  }

  // 应用筛选逻辑
  applyFilters(filters)
}
```

## 自定义样式

组件支持完全的样式自定义：

```typescript
<UniversalFilter
  configs={configs}
  onFiltersChange={handleFiltersChange}
  className="
    border-2 border-blue-200
    rounded-xl
    p-6
    bg-gradient-to-r from-blue-50 to-indigo-50
    shadow-lg
  "
/>
```

## 性能优化建议

1. **配置缓存**: 将 `configs` 定义在组件外部或使用 `useMemo`
2. **回调优化**: 使用 `useCallback` 包装 `onFiltersChange`
3. **选项数据**: 对于大量选项，考虑使用虚拟滚动或搜索功能

```typescript
const configs = useMemo(() => [
  // 配置项...
], [])

const handleFiltersChange = useCallback((filters: FilterValues) => {
  // 处理逻辑...
}, [])
```

## 常见问题

### Q: 如何支持多选枚举？
A: 当前版本不支持多选，如需多选功能，可以扩展组件或使用多个单选筛选器。

### Q: 如何自定义日期格式？
A: 组件内部使用 `date-fns` 的 `format` 函数，可以通过修改组件源码来自定义格式。

### Q: 如何添加新的筛选器类型？
A: 在 `types.ts` 中扩展 `FilterType`，然后在 `UniversalFilter.tsx` 中添加对应的渲染函数。

### Q: 如何处理异步选项数据？
A: 可以在父组件中管理异步数据，然后动态更新 `configs`：

```typescript
const [configs, setConfigs] = useState<FilterConfig[]>([])

useEffect(() => {
  fetchOptions().then(options => {
    setConfigs([
      // 其他配置...
      {
        key: 'category',
        label: '分类',
        type: 'enum',
        options: options
      }
    ])
  })
}, [])
```

## 贡献指南

如果您想要扩展或改进这个组件：

1. 保持类型安全
2. 遵循现有的代码风格
3. 添加适当的文档和示例
4. 确保向后兼容性
5. 添加单元测试（如果项目有测试框架）

## 日期选择器组件

除了通用筛选组件，我们还提供了独立的日期选择器组件：

### DatePicker - 单个日期选择器

```typescript
import { DatePicker } from '@/components/common/DatePicker'

<DatePicker
  value={date}
  onChange={setDate}
  placeholder="选择日期"
/>
```

### DateRangePicker - 日期范围选择器

```typescript
import { DateRangePicker } from '@/components/common/DatePicker'

<DateRangePicker
  value={{ start: '2024-01-01', end: '2024-01-31' }}
  onChange={setDateRange}
  placeholder={{ start: "开始日期", end: "结束日期" }}
/>
```

### QuickDateRangePicker - 快速日期范围选择器

```typescript
import { QuickDateRangePicker } from '@/components/common/DatePicker'

<QuickDateRangePicker
  value={dateRange}
  onChange={setDateRange}
/>
```

提供快捷选项：今天、昨天、最近7天、最近30天、最近3个月

## 样式优化

### 日历组件优化

- ✅ **更清晰的布局**: 优化了日历的间距和对齐
- ✅ **更好的视觉效果**: 改进了选中状态和悬停效果
- ✅ **中文本地化**: 完整的中文日期格式支持
- ✅ **响应式设计**: 在不同屏幕尺寸下都有良好表现

### 日期选择器特性

- ✅ **清除功能**: 支持一键清除已选择的日期
- ✅ **智能占位符**: 动态显示选择状态
- ✅ **键盘导航**: 支持键盘操作
- ✅ **无障碍支持**: 完整的 ARIA 标签

## 更新日志

- v1.1.0: 优化日期选择器样式和用户体验
  - 重新设计日历组件样式
  - 新增独立的日期选择器组件
  - 新增快速日期范围选择器
  - 改进中文本地化支持
- v1.0.0: 初始版本，支持基本的四种筛选类型
  - 支持的筛选类型：枚举、文本、日期、日期范围
  - 完整的 TypeScript 支持
  - 响应式设计
  - 可自定义样式
