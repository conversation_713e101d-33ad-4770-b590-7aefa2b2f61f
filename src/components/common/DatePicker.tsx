"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar as CalendarIcon, X } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cn } from '@/lib/utils'

interface DatePickerProps {
  value?: string
  onChange: (value: string | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DatePicker({
  value,
  onChange,
  placeholder = "选择日期",
  className,
  disabled = false
}: DatePickerProps) {
  const [open, setOpen] = useState(false)

  const handleSelect = (date: Date | undefined) => {
    if (date) {
      onChange(format(date, "yyyy-MM-dd"))
    } else {
      onChange(undefined)
    }
    setOpen(false)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange(undefined)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className={cn(
            "w-full h-9 justify-start text-left font-normal px-3 relative",
            !value && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4 opacity-50" />
          <span className="text-sm flex-1">
            {value ? format(new Date(value), "yyyy年MM月dd日", { locale: zhCN }) : placeholder}
          </span>
          {value && !disabled && (
            <X 
              className="h-4 w-4 opacity-50 hover:opacity-100 ml-2" 
              onClick={handleClear}
            />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={value ? new Date(value) : undefined}
          onSelect={handleSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}

interface DateRangePickerProps {
  value?: { start?: string; end?: string }
  onChange: (value: { start?: string; end?: string } | undefined) => void
  placeholder?: { start?: string; end?: string }
  className?: string
  disabled?: boolean
}

export function DateRangePicker({
  value = {},
  onChange,
  placeholder = { start: "开始日期", end: "结束日期" },
  className,
  disabled = false
}: DateRangePickerProps) {
  const { start, end } = value

  const handleStartChange = (newStart: string | undefined) => {
    const newValue = { ...value, start: newStart }
    if (!newStart) {
      delete newValue.start
    }
    if (Object.keys(newValue).length === 0) {
      onChange(undefined)
    } else {
      onChange(newValue)
    }
  }

  const handleEndChange = (newEnd: string | undefined) => {
    const newValue = { ...value, end: newEnd }
    if (!newEnd) {
      delete newValue.end
    }
    if (Object.keys(newValue).length === 0) {
      onChange(undefined)
    } else {
      onChange(newValue)
    }
  }

  return (
    <div className={cn("grid grid-cols-2 gap-2", className)}>
      <DatePicker
        value={start}
        onChange={handleStartChange}
        placeholder={placeholder.start}
        disabled={disabled}
      />
      <DatePicker
        value={end}
        onChange={handleEndChange}
        placeholder={placeholder.end}
        disabled={disabled}
      />
    </div>
  )
}

// 快速日期选择器 - 提供常用的日期范围选项
interface QuickDateRangePickerProps {
  value?: { start?: string; end?: string }
  onChange: (value: { start?: string; end?: string } | undefined) => void
  className?: string
  disabled?: boolean
}

export function QuickDateRangePicker({
  value = {},
  onChange,
  className,
  disabled = false
}: QuickDateRangePickerProps) {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  const lastWeek = new Date(today)
  lastWeek.setDate(lastWeek.getDate() - 7)
  
  const lastMonth = new Date(today)
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  
  const last3Months = new Date(today)
  last3Months.setMonth(last3Months.getMonth() - 3)

  const quickOptions = [
    {
      label: "今天",
      value: {
        start: format(today, "yyyy-MM-dd"),
        end: format(today, "yyyy-MM-dd")
      }
    },
    {
      label: "昨天",
      value: {
        start: format(yesterday, "yyyy-MM-dd"),
        end: format(yesterday, "yyyy-MM-dd")
      }
    },
    {
      label: "最近7天",
      value: {
        start: format(lastWeek, "yyyy-MM-dd"),
        end: format(today, "yyyy-MM-dd")
      }
    },
    {
      label: "最近30天",
      value: {
        start: format(lastMonth, "yyyy-MM-dd"),
        end: format(today, "yyyy-MM-dd")
      }
    },
    {
      label: "最近3个月",
      value: {
        start: format(last3Months, "yyyy-MM-dd"),
        end: format(today, "yyyy-MM-dd")
      }
    }
  ]

  return (
    <div className={cn("space-y-3", className)}>
      <DateRangePicker
        value={value}
        onChange={onChange}
        disabled={disabled}
      />
      
      <div className="flex flex-wrap gap-1">
        {quickOptions.map((option) => (
          <Button
            key={option.label}
            variant="outline"
            size="sm"
            disabled={disabled}
            className="h-7 px-2 text-xs"
            onClick={() => onChange(option.value)}
          >
            {option.label}
          </Button>
        ))}
        <Button
          variant="outline"
          size="sm"
          disabled={disabled}
          className="h-7 px-2 text-xs text-muted-foreground"
          onClick={() => onChange(undefined)}
        >
          清除
        </Button>
      </div>
    </div>
  )
}
