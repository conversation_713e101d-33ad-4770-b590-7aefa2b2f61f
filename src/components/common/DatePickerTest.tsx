"use client"

import { useState } from 'react'
import { DatePicker, DateRangePicker, QuickDateRangePicker } from './DatePicker'

export function DatePickerTest() {
  const [singleDate, setSingleDate] = useState<string | undefined>(undefined)
  const [dateRange, setDateRange] = useState<{ start?: string; end?: string } | undefined>(undefined)
  const [quickRange, setQuickRange] = useState<{ start?: string; end?: string } | undefined>(undefined)

  return (
    <div className="space-y-6 p-6 max-w-2xl">
      <h2 className="text-xl font-bold">日期选择器测试</h2>
      
      {/* 单个日期选择器测试 */}
      <div className="space-y-3 p-4 border rounded-lg">
        <h3 className="font-semibold">单个日期选择器</h3>
        <DatePicker
          value={singleDate}
          onChange={setSingleDate}
          placeholder="选择一个日期"
        />
        <div className="text-sm text-gray-600">
          选择的日期: {singleDate || '未选择'}
        </div>
        <button 
          onClick={() => setSingleDate(undefined)}
          className="text-xs bg-gray-100 px-2 py-1 rounded hover:bg-gray-200"
        >
          清除日期
        </button>
      </div>

      {/* 日期范围选择器测试 */}
      <div className="space-y-3 p-4 border rounded-lg">
        <h3 className="font-semibold">日期范围选择器</h3>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          placeholder={{ start: "开始日期", end: "结束日期" }}
        />
        <div className="text-sm text-gray-600">
          选择的范围: {dateRange?.start || '未设置'} ~ {dateRange?.end || '未设置'}
        </div>
        <button 
          onClick={() => setDateRange(undefined)}
          className="text-xs bg-gray-100 px-2 py-1 rounded hover:bg-gray-200"
        >
          清除范围
        </button>
      </div>

      {/* 快速日期范围选择器测试 */}
      <div className="space-y-3 p-4 border rounded-lg">
        <h3 className="font-semibold">快速日期范围选择器</h3>
        <QuickDateRangePicker
          value={quickRange}
          onChange={setQuickRange}
        />
        <div className="text-sm text-gray-600">
          快速选择的范围: {quickRange?.start || '未设置'} ~ {quickRange?.end || '未设置'}
        </div>
        <button 
          onClick={() => setQuickRange(undefined)}
          className="text-xs bg-gray-100 px-2 py-1 rounded hover:bg-gray-200"
        >
          清除快速范围
        </button>
      </div>

      {/* 状态显示 */}
      <div className="p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">当前状态</h3>
        <div className="space-y-2 text-sm">
          <div>单个日期: <code>{JSON.stringify(singleDate)}</code></div>
          <div>日期范围: <code>{JSON.stringify(dateRange)}</code></div>
          <div>快速范围: <code>{JSON.stringify(quickRange)}</code></div>
        </div>
      </div>

      {/* 测试说明 */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">测试说明</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 测试选择日期功能</li>
          <li>• 测试清除日期功能（点击日期选择器中的 X 按钮）</li>
          <li>• 测试外部清除按钮</li>
          <li>• 测试快速日期范围选项</li>
          <li>• 确保没有 undefined 错误</li>
        </ul>
      </div>
    </div>
  )
}
