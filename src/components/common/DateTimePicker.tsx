"use client"

import * as React from "react"
import { ChevronDownIcon, Calendar as CalendarIcon, Clock, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format, parse } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cn } from '@/lib/utils'

interface DateTimePickerProps {
  value?: string // ISO string format: "2024-01-15T10:30:00"
  onChange: (value: string | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  showSeconds?: boolean
}

export function DateTimePicker({
  value,
  onChange,
  placeholder = "选择日期时间",
  className,
  disabled = false,
  showSeconds = true
}: DateTimePickerProps) {
  const [open, setOpen] = React.useState(false)
  
  // 解析当前值
  const currentDate = value ? new Date(value) : undefined
  const currentTime = value ? format(new Date(value), showSeconds ? "HH:mm:ss" : "HH:mm") : ""

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (!selectedDate) {
      onChange(undefined)
      return
    }

    // 保持现有时间，只更新日期
    const existingTime = currentTime || (showSeconds ? "00:00:00" : "00:00")
    const [hours, minutes, seconds = "00"] = existingTime.split(":")
    
    selectedDate.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || "0"))
    onChange(selectedDate.toISOString())
  }

  const handleTimeChange = (timeString: string) => {
    if (!currentDate) {
      // 如果没有日期，使用今天的日期
      const today = new Date()
      const [hours, minutes, seconds = "00"] = timeString.split(":")
      today.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || "0"))
      onChange(today.toISOString())
      return
    }

    // 更新现有日期的时间
    const newDate = new Date(currentDate)
    const [hours, minutes, seconds = "00"] = timeString.split(":")
    newDate.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || "0"))
    onChange(newDate.toISOString())
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange(undefined)
  }

  const formatDisplayValue = () => {
    if (!value) return placeholder
    const date = new Date(value)
    const dateStr = format(date, "yyyy年MM月dd日", { locale: zhCN })
    const timeStr = format(date, showSeconds ? "HH:mm:ss" : "HH:mm")
    return `${dateStr} ${timeStr}`
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            disabled={disabled}
            className={cn(
              "w-full h-9 justify-start text-left font-normal px-3 relative",
              !value && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 opacity-50" />
            <span className="text-sm flex-1">
              {formatDisplayValue()}
            </span>
            {value && !disabled && (
              <button
                type="button"
                className="ml-2 h-4 w-4 rounded-full hover:bg-destructive/20 flex items-center justify-center transition-colors"
                onClick={handleClear}
                aria-label="清除日期时间"
              >
                <X className="h-3 w-3 opacity-50 hover:opacity-100 hover:text-destructive" />
              </button>
            )}
            <ChevronDownIcon className="ml-2 h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto overflow-hidden p-0" align="start">
          <div className="flex">
            {/* 日期选择器 */}
            <div className="border-r">
              <Calendar
                mode="single"
                selected={currentDate}
                onSelect={handleDateSelect}
                captionLayout="dropdown"
                initialFocus
              />
            </div>
            
            {/* 时间选择器 */}
            <div className="p-4 space-y-4 min-w-[200px]">
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  时间选择
                </Label>
                <Input
                  type="time"
                  step={showSeconds ? "1" : "60"}
                  value={currentTime}
                  onChange={(e) => handleTimeChange(e.target.value)}
                  className="bg-background appearance-none [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-calendar-picker-indicator]:appearance-none"
                />
              </div>
              
              {/* 快速时间选项 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">快速选择</Label>
                <div className="grid grid-cols-2 gap-1">
                  {[
                    { label: "00:00", value: showSeconds ? "00:00:00" : "00:00" },
                    { label: "09:00", value: showSeconds ? "09:00:00" : "09:00" },
                    { label: "12:00", value: showSeconds ? "12:00:00" : "12:00" },
                    { label: "18:00", value: showSeconds ? "18:00:00" : "18:00" },
                    { label: "现在", value: format(new Date(), showSeconds ? "HH:mm:ss" : "HH:mm") }
                  ].map((time) => (
                    <Button
                      key={time.label}
                      variant="outline"
                      size="sm"
                      className="h-7 text-xs"
                      onClick={() => handleTimeChange(time.value)}
                    >
                      {time.label}
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex gap-2 pt-2 border-t">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    onChange(undefined)
                    setOpen(false)
                  }}
                >
                  清除
                </Button>
                <Button
                  size="sm"
                  className="flex-1"
                  onClick={() => setOpen(false)}
                >
                  确定
                </Button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

// 简化版的日期时间选择器（只显示日期和时间，不显示秒）
export function SimpleDateTimePicker(props: Omit<DateTimePickerProps, 'showSeconds'>) {
  return <DateTimePicker {...props} showSeconds={false} />
}

// 日期时间范围选择器
interface DateTimeRangePickerProps {
  value?: { start?: string; end?: string }
  onChange: (value: { start?: string; end?: string } | undefined) => void
  placeholder?: { start?: string; end?: string }
  className?: string
  disabled?: boolean
  showSeconds?: boolean
}

export function DateTimeRangePicker({
  value,
  onChange,
  placeholder = { start: "开始时间", end: "结束时间" },
  className,
  disabled = false,
  showSeconds = true
}: DateTimeRangePickerProps) {
  const safeValue = value || {}
  const { start, end } = safeValue

  const handleStartChange = (newStart: string | undefined) => {
    const newValue = { ...safeValue, start: newStart }
    if (!newStart) {
      delete newValue.start
    }
    if (Object.keys(newValue).length === 0) {
      onChange(undefined)
    } else {
      onChange(newValue)
    }
  }

  const handleEndChange = (newEnd: string | undefined) => {
    const newValue = { ...safeValue, end: newEnd }
    if (!newEnd) {
      delete newValue.end
    }
    if (Object.keys(newValue).length === 0) {
      onChange(undefined)
    } else {
      onChange(newValue)
    }
  }

  return (
    <div className={cn("grid grid-cols-1 lg:grid-cols-2 gap-2", className)}>
      <DateTimePicker
        value={start}
        onChange={handleStartChange}
        placeholder={placeholder.start}
        disabled={disabled}
        showSeconds={showSeconds}
      />
      <DateTimePicker
        value={end}
        onChange={handleEndChange}
        placeholder={placeholder.end}
        disabled={disabled}
        showSeconds={showSeconds}
      />
    </div>
  )
}
