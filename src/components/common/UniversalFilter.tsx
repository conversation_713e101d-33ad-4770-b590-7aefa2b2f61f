"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, RotateCcw, Search } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cn } from '@/lib/utils'
import {
  FilterType,
  FilterOption,
  FilterConfig,
  FilterValues,
  UniversalFilterProps,
  DateRangeValue
} from './types'
import { DatePicker, DateRangePicker } from './DatePicker'

// 重新导出类型以保持向后兼容
export type { FilterType, FilterOption, FilterConfig, FilterValues, DateRangeValue }

export function UniversalFilter({
  configs,
  onFiltersChange,
  onActionClick,
  actionLabel,
  actionIcon,
  className,
  searchPlaceholder = "搜索..."
}: UniversalFilterProps) {
  const [filters, setFilters] = useState<FilterValues>({})
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // 获取搜索配置
  const searchConfig = configs.find(config => config.searchable)

  // 使用 useEffect 来通知父组件筛选变化，但避免无限循环
  useEffect(() => {
    const allFilters = { ...filters }
    if (searchTerm.trim() && searchConfig) {
      allFilters[searchConfig.key] = searchTerm.trim()
    } else if (searchConfig) {
      delete allFilters[searchConfig.key]
    }
    onFiltersChange(allFilters)
  }, [filters, searchTerm]) // 移除 onFiltersChange 和 searchConfig 依赖避免循环

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }, [])

  const updateFilter = useCallback((key: string, value: any) => {
    if (searchConfig && key === searchConfig.key) return // 搜索通过 searchTerm 状态管理
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value
      }
      return newFilters
    })
  }, [searchConfig])

  const removeFilter = useCallback((key: string) => {
    if (searchConfig && key === searchConfig.key) {
      setSearchTerm('')
      return
    }
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
  }, [searchConfig])

  const clearAllFilters = useCallback(() => {
    setFilters({})
    setSearchTerm('')
  }, [])

  // 计算激活的筛选器数量
  const hasActiveFilters = Object.keys(filters).length > 0 || searchTerm.trim()
  const activeFilterCount = Object.keys(filters).length + (searchTerm.trim() ? 1 : 0)

  // 渲染单个日期选择器
  const renderDatePicker = (config: FilterConfig) => {
    const value = filters[config.key]
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">{config.label}</label>
        <DatePicker
          value={value}
          onChange={(newValue) => {
            if (newValue) {
              updateFilter(config.key, newValue)
            } else {
              removeFilter(config.key)
            }
          }}
          placeholder={config.placeholder || "选择日期"}
        />
      </div>
    )
  }

  // 渲染日期范围选择器
  const renderDateRangePicker = (config: FilterConfig) => {
    const value: DateRangeValue = filters[config.key] || {}

    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">{config.label}</label>
        <DateRangePicker
          value={Object.keys(value).length > 0 ? value : undefined}
          onChange={(newValue) => {
            if (newValue && Object.keys(newValue).length > 0) {
              updateFilter(config.key, newValue)
            } else {
              removeFilter(config.key)
            }
          }}
          placeholder={{
            start: "开始日期",
            end: "结束日期"
          }}
        />
      </div>
    )
  }

  // 渲染枚举选择器
  const renderEnumSelect = (config: FilterConfig) => {
    const value = filters[config.key]
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">{config.label}</label>
        <Select
          key={`${config.key}-${value || 'all'}`}
          value={value || "all"}
          onValueChange={(selectedValue) => {
            if (selectedValue && selectedValue !== "all") {
              updateFilter(config.key, selectedValue)
            } else {
              removeFilter(config.key)
            }
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder={config.placeholder || `选择${config.label}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部{config.label}</SelectItem>
            {config.options?.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }

  // 渲染文本输入框
  const renderTextInput = (config: FilterConfig) => {
    const value = filters[config.key] || ''
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">{config.label}</label>
        <Input
          placeholder={config.placeholder || `输入${config.label}`}
          value={value}
          onChange={(e) => {
            const inputValue = e.target.value
            if (inputValue.trim()) {
              updateFilter(config.key, inputValue)
            } else {
              removeFilter(config.key)
            }
          }}
        />
      </div>
    )
  }

  // 渲染筛选器项
  const renderFilterItem = (config: FilterConfig) => {
    switch (config.type) {
      case 'enum':
        return renderEnumSelect(config)
      case 'text':
        return renderTextInput(config)
      case 'date':
        return renderDatePicker(config)
      case 'dateRange':
        return renderDateRangePicker(config)
      default:
        return null
    }
  }

  // 获取筛选器显示标签
  const getFilterDisplayLabel = (config: FilterConfig, value: any) => {
    switch (config.type) {
      case 'enum':
        const option = config.options?.find(opt => opt.value === value)
        return option ? `${config.label}: ${option.label}` : `${config.label}: ${value}`
      case 'text':
        return `${config.label}: ${value}`
      case 'date':
        return `${config.label}: ${format(new Date(value), "yyyy-MM-dd", { locale: zhCN })}`
      case 'dateRange':
        const { start, end } = value
        let rangeText = config.label + ": "
        if (start && end) {
          rangeText += `${format(new Date(start), "MM-dd", { locale: zhCN })} ~ ${format(new Date(end), "MM-dd", { locale: zhCN })}`
        } else if (start) {
          rangeText += `从 ${format(new Date(start), "MM-dd", { locale: zhCN })}`
        } else if (end) {
          rangeText += `到 ${format(new Date(end), "MM-dd", { locale: zhCN })}`
        }
        return rangeText
      default:
        return `${config.label}: ${value}`
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 搜索框和操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-4 w-full">
        {searchConfig && (
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchConfig.placeholder || searchPlaceholder}
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-9 w-full"
            />
          </div>
        )}
        {onActionClick && actionLabel && (
          <Button onClick={onActionClick} className="flex-shrink-0">
            {actionIcon}
            {actionLabel}
          </Button>
        )}
      </div>

      {/* 过滤器切换按钮 */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          筛选条件
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-1">
              {activeFilterCount}
            </Badge>
          )}
        </Button>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <RotateCcw className="h-4 w-4" />
            清除筛选
          </Button>
        )}
      </div>

      {/* 筛选器面板 */}
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50">
          {configs
            .filter(config => !config.searchable) // 排除搜索配置
            .map((config) => (
              <div key={config.key}>
                {renderFilterItem(config)}
              </div>
            ))}
        </div>
      )}

      {/* 激活的筛选器标签 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {searchTerm.trim() && searchConfig && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {searchConfig.label}: {searchTerm}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  removeFilter(searchConfig.key)
                }}
              />
            </Badge>
          )}
          {Object.entries(filters).map(([key, value]) => {
            const config = configs.find(c => c.key === key)
            if (!config) return null

            return (
              <Badge key={key} variant="secondary" className="flex items-center gap-1">
                {getFilterDisplayLabel(config, value)}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    removeFilter(key)
                  }}
                />
              </Badge>
            )
          })}
        </div>
      )}
    </div>
  )
}
