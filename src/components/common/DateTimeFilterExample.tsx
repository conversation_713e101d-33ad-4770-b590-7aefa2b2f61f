"use client"

import { useState, useCallback } from 'react'
import { UniversalFilter, FilterConfig, FilterValues } from './UniversalFilter'
import { DateTimePicker, DateTimeRangePicker } from './DateTimePicker'
import { PlusCircle, Clock, Calendar } from 'lucide-react'

// 日期时间筛选配置示例
const dateTimeConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索事件名称...',
    searchable: true
  },
  {
    key: 'eventType',
    label: '事件类型',
    type: 'enum',
    options: [
      { value: 'meeting', label: '📅 会议' },
      { value: 'task', label: '📋 任务' },
      { value: 'reminder', label: '⏰ 提醒' },
      { value: 'appointment', label: '🏥 预约' }
    ]
  },
  {
    key: 'priority',
    label: '优先级',
    type: 'enum',
    options: [
      { value: 'urgent', label: '🔴 紧急' },
      { value: 'high', label: '🟠 高' },
      { value: 'normal', label: '🟡 普通' },
      { value: 'low', label: '🟢 低' }
    ]
  },
  {
    key: 'startTime',
    label: '开始时间',
    type: 'datetime',
    placeholder: '选择开始时间'
  },
  {
    key: 'timeRange',
    label: '时间范围',
    type: 'datetimeRange'
  },
  {
    key: 'dueDate',
    label: '截止日期',
    type: 'date',
    placeholder: '选择截止日期'
  }
]

export function DateTimeFilterExample() {
  const [filters, setFilters] = useState<FilterValues>({})
  const [singleDateTime, setSingleDateTime] = useState<string | undefined>(undefined)
  const [dateTimeRange, setDateTimeRange] = useState<{ start?: string; end?: string } | undefined>(undefined)

  const handleFiltersChange = useCallback((newFilters: FilterValues) => {
    console.log('筛选条件变化:', newFilters)
    setFilters(newFilters)
  }, [])

  const handleAddClick = useCallback(() => {
    console.log('点击新建事件')
  }, [])

  // 模拟的事件数据
  const mockEvents = [
    { 
      id: 1, 
      name: '项目启动会议', 
      type: 'meeting', 
      priority: 'high',
      startTime: '2024-01-15T09:00:00',
      endTime: '2024-01-15T10:30:00'
    },
    { 
      id: 2, 
      name: '代码审查', 
      type: 'task', 
      priority: 'normal',
      startTime: '2024-01-15T14:00:00',
      endTime: '2024-01-15T15:00:00'
    },
    { 
      id: 3, 
      name: '客户演示', 
      type: 'meeting', 
      priority: 'urgent',
      startTime: '2024-01-16T10:00:00',
      endTime: '2024-01-16T11:00:00'
    },
    { 
      id: 4, 
      name: '医生预约', 
      type: 'appointment', 
      priority: 'high',
      startTime: '2024-01-17T15:30:00',
      endTime: '2024-01-17T16:00:00'
    }
  ]

  // 根据筛选条件过滤事件
  const filteredEvents = mockEvents.filter(event => {
    if (filters.search && !event.name.toLowerCase().includes(filters.search.toLowerCase())) {
      return false
    }
    if (filters.eventType && event.type !== filters.eventType) {
      return false
    }
    if (filters.priority && event.priority !== filters.priority) {
      return false
    }
    // 这里可以添加更多的时间筛选逻辑
    return true
  })

  const getTypeColor = (type: string) => {
    const colors = {
      meeting: 'bg-blue-100 text-blue-800 border-blue-200',
      task: 'bg-green-100 text-green-800 border-green-200',
      reminder: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      appointment: 'bg-purple-100 text-purple-800 border-purple-200'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      urgent: 'bg-red-500 text-white',
      high: 'bg-orange-500 text-white',
      normal: 'bg-blue-500 text-white',
      low: 'bg-green-500 text-white'
    }
    return colors[priority as keyof typeof colors] || 'bg-gray-500 text-white'
  }

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Clock className="h-6 w-6 text-purple-600" />
          <h1 className="text-2xl font-bold">日期时间筛选组件示例</h1>
        </div>
        <p className="text-gray-600">
          展示支持时分秒的日期时间选择器和筛选功能
        </p>
      </div>

      {/* 主要筛选组件 */}
      <UniversalFilter
        configs={dateTimeConfigs}
        onFiltersChange={handleFiltersChange}
        onActionClick={handleAddClick}
        actionLabel="新建事件"
        actionIcon={<PlusCircle className="mr-2 h-4 w-4" />}
        className="border rounded-lg p-4 bg-white shadow-sm"
      />

      {/* 独立日期时间选择器示例 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <div className="flex items-center gap-2 mb-3">
            <Calendar className="h-4 w-4 text-blue-600" />
            <h3 className="text-lg font-semibold">单个日期时间选择器</h3>
          </div>
          <DateTimePicker
            value={singleDateTime}
            onChange={setSingleDateTime}
            placeholder="选择日期和时间"
          />
          {singleDateTime && (
            <div className="mt-3 p-2 bg-blue-50 rounded text-sm text-blue-700">
              选择的时间: {formatDateTime(singleDateTime)}
            </div>
          )}
        </div>

        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <div className="flex items-center gap-2 mb-3">
            <Clock className="h-4 w-4 text-green-600" />
            <h3 className="text-lg font-semibold">日期时间范围选择器</h3>
          </div>
          <DateTimeRangePicker
            value={dateTimeRange}
            onChange={setDateTimeRange}
            placeholder={{
              start: "开始时间",
              end: "结束时间"
            }}
          />
          {dateTimeRange && (dateTimeRange.start || dateTimeRange.end) && (
            <div className="mt-3 p-2 bg-green-50 rounded text-sm text-green-700">
              时间范围: {dateTimeRange.start ? formatDateTime(dateTimeRange.start) : '未设置'} ~ {dateTimeRange.end ? formatDateTime(dateTimeRange.end) : '未设置'}
            </div>
          )}
        </div>
      </div>

      {/* 筛选结果展示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 事件列表 */}
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <h3 className="text-lg font-semibold mb-4">
            事件列表 ({filteredEvents.length}/{mockEvents.length})
          </h3>
          <div className="space-y-3">
            {filteredEvents.map(event => (
              <div key={event.id} className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{event.name}</h4>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(event.priority)}`}>
                    {event.priority}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                  <span className={`px-2 py-1 rounded border text-xs ${getTypeColor(event.type)}`}>
                    {event.type}
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  <div>开始: {formatDateTime(event.startTime)}</div>
                  <div>结束: {formatDateTime(event.endTime)}</div>
                </div>
              </div>
            ))}
            {filteredEvents.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Clock className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>没有找到匹配的事件</p>
              </div>
            )}
          </div>
        </div>

        {/* 筛选条件详情 */}
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <h3 className="text-lg font-semibold mb-4">当前筛选条件</h3>
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">主要筛选器</h4>
              <pre className="text-xs text-gray-600 bg-gray-50 p-3 rounded whitespace-pre-wrap">
                {JSON.stringify(filters, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">独立选择器</h4>
              <div className="text-xs text-gray-600 bg-gray-50 p-3 rounded space-y-1">
                <div>单个时间: {singleDateTime || '未选择'}</div>
                <div>时间范围: {JSON.stringify(dateTimeRange || {}, null, 2)}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 功能说明 */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2 text-purple-800">🕐 日期时间功能特点</h3>
        <ul className="text-sm text-purple-700 space-y-1">
          <li>• <strong>精确到秒</strong>: 支持时分秒的精确时间选择</li>
          <li>• <strong>日历下拉</strong>: 年月下拉选择，快速导航</li>
          <li>• <strong>快速时间</strong>: 提供常用时间点的快速选择</li>
          <li>• <strong>时间范围</strong>: 支持开始和结束时间的范围选择</li>
          <li>• <strong>中文格式</strong>: 完整的中文日期时间显示</li>
          <li>• <strong>清除功能</strong>: 支持一键清除已选择的时间</li>
        </ul>
      </div>
    </div>
  )
}
