"use client"

import { useCallback } from 'react'
import { UniversalFilter, FilterConfig, FilterValues, FilterOption } from './UniversalFilter'
import { PlusCircle } from 'lucide-react'
import {
  DEPARTMENT_OPTIONS,
  POSITION_OPTIONS,
  REGION_OPTIONS,
  GENDER_OPTIONS
} from '@/constants/staff'

// 将现有的员工筛选配置转换为通用筛选配置
const staffFilterConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索员工...',
    searchable: true
  },
  {
    key: 'department',
    label: '部门',
    type: 'enum',
    placeholder: '选择部门',
    options: DEPARTMENT_OPTIONS as readonly FilterOption[]
  },
  {
    key: 'position',
    label: '职位',
    type: 'enum',
    placeholder: '选择职位',
    options: POSITION_OPTIONS as readonly FilterOption[]
  },
  {
    key: 'region',
    label: '区域',
    type: 'enum',
    placeholder: '选择区域',
    options: REGION_OPTIONS as readonly FilterOption[]
  },
  {
    key: 'gender',
    label: '性别',
    type: 'enum',
    placeholder: '选择性别',
    options: GENDER_OPTIONS as readonly FilterOption[]
  }
]

// 兼容原有的 StaffFilters 接口
export interface StaffFilters {
  department?: string
  position?: string
  region?: string
  gender?: string
  search?: string
}

interface StaffFilterReplacementProps {
  onFiltersChange: (filters: StaffFilters) => void
  onAddClick: () => void
  className?: string
}

export function StaffFilterReplacement({
  onFiltersChange,
  onAddClick,
  className
}: StaffFilterReplacementProps) {

  const handleFiltersChange = useCallback((filters: FilterValues) => {
    // 转换为原有的 StaffFilters 格式
    const staffFilters: StaffFilters = {}

    if (filters.search) staffFilters.search = filters.search
    if (filters.department) staffFilters.department = filters.department
    if (filters.position) staffFilters.position = filters.position
    if (filters.region) staffFilters.region = filters.region
    if (filters.gender) staffFilters.gender = filters.gender

    onFiltersChange(staffFilters)
  }, [onFiltersChange])

  return (
    <UniversalFilter
      configs={staffFilterConfigs}
      onFiltersChange={handleFiltersChange}
      onActionClick={onAddClick}
      actionLabel="添加员工"
      actionIcon={<PlusCircle className="mr-2 h-4 w-4" />}
      className={className}
      searchPlaceholder="搜索员工..."
    />
  )
}

// 使用示例：
// 
// 替换原有的 StaffFilter 组件：
// 
// // 原来的代码
// import { StaffFilter } from '@/components/staff-management/StaffFilter'
// 
// // 替换为
// import { StaffFilterReplacement as StaffFilter } from '@/components/common/StaffFilterReplacement'
// 
// // 使用方式完全相同
// <StaffFilter
//   onFiltersChange={handleFiltersChange}
//   onAddClick={handleAddClick}
//   className="my-custom-class"
// />
