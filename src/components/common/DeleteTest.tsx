"use client"

import { useState, useCallback } from 'react'
import { UniversalFilter, FilterConfig, FilterValues } from './UniversalFilter'
import { DatePicker } from './DatePicker'
import { PlusCircle } from 'lucide-react'

// 简单的测试配置
const deleteTestConfigs: FilterConfig[] = [
  {
    key: 'search',
    label: '搜索',
    type: 'text',
    placeholder: '搜索内容...',
    searchable: true
  },
  {
    key: 'status',
    label: '状态',
    type: 'enum',
    options: [
      { value: 'active', label: '激活' },
      { value: 'inactive', label: '非激活' },
      { value: 'pending', label: '待处理' }
    ]
  },
  {
    key: 'priority',
    label: '优先级',
    type: 'enum',
    options: [
      { value: 'high', label: '高' },
      { value: 'medium', label: '中' },
      { value: 'low', label: '低' }
    ]
  },
  {
    key: 'date',
    label: '日期',
    type: 'date'
  },
  {
    key: 'dateRange',
    label: '日期范围',
    type: 'dateRange'
  }
]

export function DeleteTest() {
  const [filters, setFilters] = useState<FilterValues>({})
  const [testDate, setTestDate] = useState<string | undefined>('2024-01-15')

  const handleFiltersChange = useCallback((newFilters: FilterValues) => {
    console.log('筛选条件变化:', newFilters)
    setFilters(newFilters)
  }, [])

  const handleAddClick = useCallback(() => {
    console.log('点击添加按钮')
  }, [])

  // 预设一些筛选条件用于测试删除
  const presetFilters = () => {
    setFilters({
      search: '测试搜索',
      status: 'active',
      priority: 'high',
      date: '2024-01-15',
      dateRange: { start: '2024-01-01', end: '2024-01-31' }
    })
  }

  const clearAllFilters = () => {
    setFilters({})
  }

  return (
    <div className="space-y-6 p-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-2">删除功能测试</h2>
        <p className="text-sm text-gray-600 mb-4">
          测试筛选标签和日期选择器中的 X 按钮删除功能
        </p>
        
        <div className="flex gap-2">
          <button
            onClick={presetFilters}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            设置测试数据
          </button>
          <button
            onClick={clearAllFilters}
            className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
          >
            清除所有筛选
          </button>
        </div>
      </div>

      {/* 通用筛选组件测试 */}
      <div className="border rounded-lg p-4 bg-white">
        <h3 className="text-lg font-semibold mb-4">通用筛选组件</h3>
        <UniversalFilter
          configs={deleteTestConfigs}
          onFiltersChange={handleFiltersChange}
          onActionClick={handleAddClick}
          actionLabel="添加项目"
          actionIcon={<PlusCircle className="mr-2 h-4 w-4" />}
        />
      </div>

      {/* 独立日期选择器测试 */}
      <div className="border rounded-lg p-4 bg-white">
        <h3 className="text-lg font-semibold mb-4">独立日期选择器</h3>
        <div className="max-w-xs">
          <DatePicker
            value={testDate}
            onChange={setTestDate}
            placeholder="选择测试日期"
          />
        </div>
        <div className="mt-2 text-sm text-gray-600">
          当前值: {testDate || '未选择'}
        </div>
      </div>

      {/* 当前状态显示 */}
      <div className="border rounded-lg p-4 bg-gray-50">
        <h3 className="text-lg font-semibold mb-2">当前状态</h3>
        <div className="space-y-2">
          <div>
            <strong>筛选器状态:</strong>
            <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
              {JSON.stringify(filters, null, 2)}
            </pre>
          </div>
          <div>
            <strong>独立日期选择器:</strong>
            <code className="ml-2 bg-white px-2 py-1 rounded text-sm">
              {JSON.stringify(testDate)}
            </code>
          </div>
        </div>
      </div>

      {/* 测试指南 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">🧪 测试指南</h3>
        <div className="text-sm text-blue-700 space-y-2">
          <div>
            <strong>1. 筛选标签删除测试:</strong>
            <ul className="ml-4 mt-1 space-y-1">
              <li>• 点击"设置测试数据"按钮</li>
              <li>• 尝试点击每个筛选标签右侧的 X 按钮</li>
              <li>• 确认对应的筛选条件被移除</li>
            </ul>
          </div>
          
          <div>
            <strong>2. 日期选择器删除测试:</strong>
            <ul className="ml-4 mt-1 space-y-1">
              <li>• 独立日期选择器已预设日期</li>
              <li>• 点击日期选择器右侧的 X 按钮</li>
              <li>• 确认日期被清除</li>
            </ul>
          </div>
          
          <div>
            <strong>3. 预期行为:</strong>
            <ul className="ml-4 mt-1 space-y-1">
              <li>• X 按钮应该有悬停效果</li>
              <li>• 点击 X 按钮应该立即移除对应项</li>
              <li>• 不应该有任何错误或异常</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
