"use client"

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, RotateCcw, Search, PlusCircle } from 'lucide-react'
import {
  Department,
  Position,
  Region,
  Gender,
  DEPARTMENT_OPTIONS,
  POSITION_OPTIONS,
  REGION_OPTIONS,
  GENDER_OPTIONS
} from '@/constants/staff'

export interface StaffFilters {
  department?: Department
  position?: Position
  region?: Region
  gender?: Gender
  search?: string
}

interface StaffFilterProps {
  onFiltersChange: (filters: StaffFilters) => void
  onAddClick: () => void
  className?: string
}

export function StaffFilter({ onFiltersChange, onAddClick, className }: StaffFilterProps) {
  const [filters, setFilters] = useState<StaffFilters>({})
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // 使用 useEffect 来通知父组件筛选变化，但避免无限循环
  useEffect(() => {
    const allFilters = { ...filters }
    if (searchTerm.trim()) {
      allFilters.search = searchTerm.trim()
    } else {
      delete allFilters.search
    }
    onFiltersChange(allFilters)
  }, [filters, searchTerm]) // 移除 onFiltersChange 依赖避免循环

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }, [])

  const updateFilter = useCallback((key: keyof StaffFilters, value: any) => {
    if (key === 'search') return // 搜索通过 searchTerm 状态管理
    console.log('updateFilter:', key, value) // 调试日志
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value
      }
      console.log('新的筛选状态:', newFilters) // 调试日志
      return newFilters
    })
  }, [])

  const removeFilter = useCallback((key: keyof StaffFilters) => {
    console.log('removeFilter:', key) // 调试日志
    if (key === 'search') {
      setSearchTerm('')
      return
    }
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      console.log('移除筛选后的状态:', newFilters) // 调试日志
      return newFilters
    })
  }, [])

  const clearAllFilters = useCallback(() => {
    setFilters({})
    setSearchTerm('')
  }, [])

  const hasActiveFilters = Object.keys(filters).length > 0 || searchTerm.trim()
  const activeFilterCount = Object.keys(filters).length + (searchTerm.trim() ? 1 : 0)

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 搜索框和添加按钮 */}
      <div className="flex flex-col sm:flex-row gap-4 w-full">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索员工..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-9 w-full"
          />
        </div>
        <Button onClick={onAddClick} className="flex-shrink-0">
          <PlusCircle className="mr-2 h-4 w-4" />
          添加员工
        </Button>
      </div>

      {/* 过滤器切换按钮 */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          筛选条件
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-1">
              {activeFilterCount}
            </Badge>
          )}
        </Button>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <RotateCcw className="h-4 w-4" />
            清除筛选
          </Button>
        )}
      </div>

      {/* 筛选器面板 */}
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50">
          {/* 部门筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">部门</label>
            <Select
              key={`department-${filters.department || 'all'}`}
              value={filters.department || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  updateFilter('department', value as Department)
                } else {
                  removeFilter('department')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择部门" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部部门</SelectItem>
                {DEPARTMENT_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 职位筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">职位</label>
            <Select
              key={`position-${filters.position || 'all'}`}
              value={filters.position || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  updateFilter('position', value as Position)
                } else {
                  removeFilter('position')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择职位" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部职位</SelectItem>
                {POSITION_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 区域筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">区域</label>
            <Select
              key={`region-${filters.region || 'all'}`}
              value={filters.region || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  updateFilter('region', value as Region)
                } else {
                  removeFilter('region')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择区域" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部区域</SelectItem>
                {REGION_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 性别筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">性别</label>
            <Select
              key={`gender-${filters.gender || 'all'}`}
              value={filters.gender || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  updateFilter('gender', value as Gender)
                } else {
                  removeFilter('gender')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择性别" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部性别</SelectItem>
                {GENDER_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* 激活的筛选器标签 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {searchTerm.trim() && (
            <Badge variant="secondary" className="flex items-center gap-1">
              搜索: {searchTerm}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  removeFilter('search')
                }}
              />
            </Badge>
          )}
          {filters.department && (
            <Badge variant="secondary" className="flex items-center gap-1">
              部门: {DEPARTMENT_OPTIONS.find(opt => opt.value === filters.department)?.label}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  removeFilter('department')
                }}
              />
            </Badge>
          )}
          {filters.position && (
            <Badge variant="secondary" className="flex items-center gap-1">
              职位: {POSITION_OPTIONS.find(opt => opt.value === filters.position)?.label}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  removeFilter('position')
                }}
              />
            </Badge>
          )}
          {filters.region && (
            <Badge variant="secondary" className="flex items-center gap-1">
              区域: {REGION_OPTIONS.find(opt => opt.value === filters.region)?.label}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  removeFilter('region')
                }}
              />
            </Badge>
          )}
          {filters.gender && (
            <Badge variant="secondary" className="flex items-center gap-1">
              性别: {GENDER_OPTIONS.find(opt => opt.value === filters.gender)?.label}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  removeFilter('gender')
                }}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
} 