"use client"

import {useEffect, useState} from 'react'
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>lose, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle,} from '@/components/ui/dialog'
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {Label} from '@/components/ui/label'
import {Textarea} from '@/components/ui/textarea'
import {CalendarIcon, Loader2} from 'lucide-react'
import {Staff, CreateStaffRequest, UpdateStaffRequest} from '@/api-real/staff'
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '../ui/select'
import {
  DEPARTMENT_OPTIONS,
  POSITION_OPTIONS,
  REGION_OPTIONS,
  GENDER_OPTIONS,
  Department,
  Position,
  Region,
  Gender
} from '@/constants/staff'
import {toast} from 'sonner'
import {Calendar} from '../ui/calendar'
import {format} from "date-fns"
import {Popover, PopoverContent, PopoverTrigger} from '../ui/popover'
import {cn} from '@/lib/utils'


interface StaffDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    staff?: Staff
    onSave: (staff: CreateStaffRequest | (UpdateStaffRequest & { uid?: string })) => void
    title: string
    isSaving: boolean
}

export function StaffsManagementDialog({
                                 open,
                                 onOpenChange,
                                 staff,
                                 onSave,
                                 title,
                                 isSaving,
                             }: StaffDialogProps) {

    // 表单数据
    const [formData, setFormData] = useState<{
        name: string;
        gender: Gender | undefined;
        department: Department | undefined;
        position: Position | undefined;
        region: Region | undefined;
        join_date: string;
        contract_end_date: string;
        phone: string;
        email: string;
        remark: string;
    }>({
        name: '',
        gender: undefined,
        department: undefined,
        position: undefined,
        region: undefined,
        join_date: '',
        contract_end_date: '',
        phone: '',
        email: '',
        remark: '',
    })

    // 错误信息
    const [errors, setErrors] = useState<{
        name?: string;
        gender?: string;
        department?: string;
        position?: string;
        region?: string;
        join_date?: string;
        contract_end_date?: string;
        phone?: string;
        email?: string;
    }>({})

    // 当staff变化时更新表单数据
    useEffect(() => {
        if (staff) {
            setFormData({
                name: staff.name || '',
                gender: staff.gender || undefined,
                department: staff.department as Department | undefined,
                position: staff.position as Position | undefined,
                region: staff.region as Region | undefined,
                join_date: staff.join_date || '',
                contract_end_date: staff.contract_end_date || '',
                phone: staff.phone || '',
                email: staff.email || '',
                remark: staff.remark || '',
            })
        } else {
            setFormData({
                name: '',
                gender: undefined,
                department: undefined,
                position: undefined,
                region: undefined,
                join_date: '',
                contract_end_date: '',
                phone: '',
                email: '',
                remark: '',
            })
        }
        // 重置错误
        setErrors({})
    }, [staff, open])

    // 验证表单
    const validateForm = (): boolean => {
        const newErrors: {
            name?: string;
            phone?: string;
            email?: string;
            gender?: string;
            department?: string;
            position?: string;
            join_date?: string;
            contract_end_date?: string;
        } = {}

        if (!formData.name.trim()) {
            newErrors.name = '员工名称不能为空'
        }
        // 性别错误
        if (!formData.gender) {
            newErrors.gender = '请选择新员工性别哦！'
        }
        // 电话
        if (!formData.phone.trim()) {
            newErrors.phone = '请填写新员工联系电话哦！'
        }
        // 邮箱
        if (!formData.email.trim()) {
            newErrors.email = '请填写新员工邮箱哦！'
        }
        // 部门
        if (!formData.department) {
            newErrors.department = '请选择新员工部门哦！'
        }
        // 职位
        if (!formData.position) {
            newErrors.position = '请选择新员工职位哦！'
        }
        // 区域
        if (!formData.region) {
            newErrors.region = '请选择新员工区域哦！'
        }
        // 加入日期
        if (!formData.join_date.trim()) {
            newErrors.join_date = '请填写新员工加入日期哦！'
        }
        // 合同到期日期
        if (!formData.contract_end_date.trim()) {
            newErrors.contract_end_date = '请填写新员工合同到期日期哦！'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    // 提交表单
    const handleSubmit = () => {
        if (validateForm() && !isSaving) {
            const submitData = {
                ...(staff?.uid ? {uid: staff.uid} : {}),
                name: formData.name.trim(),
                phone: formData.phone.trim(),
                email: formData.email.trim(),
                gender: formData.gender!,
                department: formData.department!,
                position: formData.position!,
                region: formData.region!,
                join_date: formData.join_date,
                contract_end_date: formData.contract_end_date,
                remark: formData.remark.trim(),
            }
            onSave(submitData)
        }
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="w-[95%] max-h-[92%] overflow-y-auto [&::-webkit-scrollbar]:w-0">
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                        <Label htmlFor="staff-name">
                            员工名称 <span className="text-destructive">*</span>
                        </Label>
                        <Input
                            id="staff-name"
                            value={formData.name}
                            onChange={(e) => setFormData({...formData, name: e.target.value})}
                            placeholder="请输入员工名称"
                            aria-invalid={!!errors.name}
                        />
                        {errors.name && (
                            <p className="text-sm text-destructive">{errors.name}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-gender">
                            性别 <span className="text-destructive">*</span>
                        </Label>
                        <Select
                            value={formData.gender}
                            onValueChange={(value) => setFormData({...formData, gender: value as Gender})}
                        >
                            <SelectTrigger
                                aria-invalid={!!errors.gender}
                            >
                                <SelectValue placeholder="请选择性别"/>
                            </SelectTrigger>
                            <SelectContent>
                                {GENDER_OPTIONS.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.gender && (
                            <p className="text-sm text-destructive">{errors.gender}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-phone">
                            电话 <span className="text-destructive">*</span>
                        </Label>
                        <Input
                            id="staff-phone"
                            value={formData.phone}
                            onChange={(e) => setFormData({...formData, phone: e.target.value})}
                            placeholder="请输入联系电话"
                            aria-invalid={!!errors.phone}
                        />
                        {errors.phone && (
                            <p className="text-sm text-destructive">{errors.phone}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-email">
                            邮箱 <span className="text-destructive">*</span>
                        </Label>
                        <Input
                            id="staff-email"
                            value={formData.email}
                            onChange={(e) => setFormData({...formData, email: e.target.value})}
                            placeholder="请输入邮箱"
                            aria-invalid={!!errors.email}
                        />
                        {errors.email && (
                            <p className="text-sm text-destructive">{errors.email}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-department">
                            部门 <span className="text-destructive">*</span>
                        </Label>
                        <Select
                            value={formData.department}
                            onValueChange={(value) => setFormData({...formData, department: value as Department})}
                        >
                            <SelectTrigger
                                aria-invalid={!!errors.department}
                            >
                                <SelectValue placeholder="请选择部门"/>
                            </SelectTrigger>
                            <SelectContent>
                                {DEPARTMENT_OPTIONS.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.department && (
                            <p className="text-sm text-destructive">{errors.department}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-position">
                            职位 <span className="text-destructive">*</span>
                        </Label>
                        <Select
                            value={formData.position}
                            onValueChange={(value) => setFormData({...formData, position: value as Position})}
                        >
                            <SelectTrigger
                                aria-invalid={!!errors.position}
                            >
                                <SelectValue placeholder="请选择职位"/>
                            </SelectTrigger>
                            <SelectContent>
                                {POSITION_OPTIONS.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.position && (
                            <p className="text-sm text-destructive">{errors.position}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-region">
                            区域 <span className="text-destructive">*</span>
                        </Label>
                        <Select
                            value={formData.region}
                            onValueChange={(value) => setFormData({...formData, region: value as Region})}
                        >
                            <SelectTrigger
                                aria-invalid={!!errors.region}
                            >
                                <SelectValue placeholder="请选择区域"/>
                            </SelectTrigger>
                            <SelectContent>
                                {REGION_OPTIONS.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.region && (
                            <p className="text-sm text-destructive">{errors.region}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-join-date">
                            加入日期 <span className="text-destructive">*</span>
                        </Label>
                        <Popover>
                            <PopoverTrigger aria-invalid={!!errors.join_date} asChild>
                                <Button
                                    variant={"outline"}
                                    className={cn(
                                        "justify-start text-left font-normal",
                                        !formData.join_date && "text-muted-foreground"
                                    )}
                                >
                                    <CalendarIcon/>
                                    {formData.join_date ? (
                                        format(new Date(formData.join_date), "yyyy-MM-dd")
                                    ) : (
                                        <span>请选择日期</span>
                                    )}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="p-0" align="start">
                                <Calendar
                                    mode="single"
                                    selected={formData.join_date ? new Date(formData.join_date) : undefined}
                                    onSelect={(date: Date | undefined) => setFormData({
                                        ...formData,
                                        join_date: date ? format(date, "yyyy-MM-dd") : ""
                                    })}
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>
                        {errors.join_date && (
                            <p className="text-sm text-destructive">{errors.join_date}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-contract-expiry-date">
                            合同到期日期 <span className="text-destructive">*</span>
                        </Label>
                        <Popover>
                            <PopoverTrigger aria-invalid={!!errors.contract_end_date} asChild>
                                <Button
                                    variant={"outline"}
                                    className={cn(
                                        " justify-start text-left font-normal",
                                        !formData.contract_end_date && "text-muted-foreground"
                                    )}
                                >
                                    <CalendarIcon/>
                                    {formData.contract_end_date ? (
                                        format(new Date(formData.contract_end_date), "yyyy-MM-dd")
                                    ) : (
                                        <span>请选择日期</span>
                                    )}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                    mode="single"
                                    selected={formData.contract_end_date ? new Date(formData.contract_end_date) : undefined}
                                    onSelect={(date: Date | undefined) => setFormData({
                                        ...formData,
                                        contract_end_date: date ? format(date, "yyyy-MM-dd") : ""
                                    })}
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>
                        {errors.contract_end_date && (
                            <p className="text-sm text-destructive">{errors.contract_end_date}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="staff-remark">
                            备注
                        </Label>
                        <Textarea
                            id="staff-remark"
                            value={formData.remark}
                            onChange={(e) => setFormData({...formData, remark: e.target.value})}
                            placeholder="请输入备注信息"
                            className="resize-none h-34"
                            rows={3}
                        />
                    </div>


                </div>
                <DialogFooter className="gap-2 sm:gap-2">
                    <DialogClose asChild>
                        <Button variant="outline" disabled={isSaving}>取消</Button>
                    </DialogClose>
                    <Button onClick={handleSubmit} disabled={isSaving}>
                        {isSaving ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                                保存中...
                            </>
                        ) : (
                            '保存'
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
} 