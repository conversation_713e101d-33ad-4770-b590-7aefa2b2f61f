"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Staff } from '@/api-real/staff'
import { GENDER_DISPLAY_MAP, DEPARTMENT_DISPLAY_MAP, POSITION_DISPLAY_MAP, REGION_DISPLAY_MAP } from '@/constants/staff'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  User,
  Phone,
  Building,
  Briefcase,
  Calendar,
  CalendarClock,
  MessageSquare,
  UserCheck
} from 'lucide-react'

interface StaffDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  staff?: Staff
  onEdit?: (staff: Staff) => void
}

export function StaffDetailDialog({
  open,
  onOpenChange,
  staff,
  onEdit,
}: StaffDetailDialogProps) {
  if (!staff) return null

  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    try {
      return format(new Date(dateString), 'yyyy年MM月dd日', { locale: zhCN })
    } catch {
      return dateString
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[90vw] !max-w-3xl max-h-[85vh] overflow-y-auto" style={{ maxWidth: '48rem' }}>
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-lg">
            <UserCheck className="h-5 w-5 text-primary" />
            员工档案详情
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="border rounded-lg p-4">
            <h3 className="text-base font-semibold flex items-center gap-2 mb-4 text-gray-900 border-b pb-2">
              <User className="h-4 w-4" />
              基本信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">姓名</label>
                <p className="text-base text-gray-900">{staff.name}</p>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">性别</label>
                <p className="text-base text-gray-900">{GENDER_DISPLAY_MAP[staff.gender] || '未知'}</p>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">员工编号</label>
                <div>
                  <span className="text-base font-mono text-gray-900 bg-gray-50 px-2 py-1 rounded inline-block">{staff.uid}</span>
                </div>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">密码状态</label>
                <div>
                  <Badge variant={staff.pwd_status ? "default" : "destructive"} className="text-xs">
                    {staff.pwd_status ? "已修改密码" : "默认密码"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* 联系信息 */}
          <div className="border rounded-lg p-4">
            <h3 className="text-base font-semibold flex items-center gap-2 mb-4 text-gray-900 border-b pb-2">
              <Phone className="h-4 w-4" />
              联系信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">电话</label>
                <p className="text-base text-gray-900">{staff.phone || '-'}</p>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">邮箱</label>
                <p className="text-base text-gray-900 break-all">{staff.email || '-'}</p>
              </div>
            </div>
          </div>

          {/* 职位信息 */}
          <div className="border rounded-lg p-4">
            <h3 className="text-base font-semibold flex items-center gap-2 mb-4 text-gray-900 border-b pb-2">
              <Briefcase className="h-4 w-4" />
              职位信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">部门</label>
                <div>
                  <Badge variant="outline" className="text-sm">
                    <Building className="h-3 w-3 mr-1" />
                    {DEPARTMENT_DISPLAY_MAP[staff.department as keyof typeof DEPARTMENT_DISPLAY_MAP] || '-'}
                  </Badge>
                </div>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">职位</label>
                <div>
                  <Badge variant="secondary" className="text-sm">
                    <Briefcase className="h-3 w-3 mr-1" />
                    {POSITION_DISPLAY_MAP[staff.position as keyof typeof POSITION_DISPLAY_MAP] || '-'}
                  </Badge>
                </div>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">区域</label>
                <div>
                  <Badge variant="default" className="text-sm">
                    <Building className="h-3 w-3 mr-1" />
                    {REGION_DISPLAY_MAP[staff.region as keyof typeof REGION_DISPLAY_MAP] || '-'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="border rounded-lg p-4">
            <h3 className="text-base font-semibold flex items-center gap-2 mb-4 text-gray-900 border-b pb-2">
              <Calendar className="h-4 w-4" />
              时间信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">入职日期</label>
                <div>
                  <span className="inline-flex items-center gap-2 text-sm text-gray-700 bg-blue-50 border border-blue-200 px-2 py-1 rounded">
                    <Calendar className="h-3 w-3 text-blue-600" />
                    {formatDate(staff.join_date)}
                  </span>
                </div>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">合同到期日期</label>
                <div>
                  <span className="inline-flex items-center gap-2 text-sm text-gray-700 bg-orange-50 border border-orange-200 px-2 py-1 rounded">
                    <CalendarClock className="h-3 w-3 text-orange-600" />
                    {formatDate(staff.contract_end_date)}
                  </span>
                </div>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">最后登录</label>
                <div>
                  <span className="inline-flex items-center text-sm text-gray-600 bg-gray-50 border border-gray-200 px-2 py-1 rounded">
                    {staff.last_login_time ? formatDate(staff.last_login_time) : '从未登录'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 备注信息 */}
          {staff.remark && (
            <div className="border rounded-lg p-4">
              <h3 className="text-base font-semibold flex items-center gap-2 mb-4 text-gray-900 border-b pb-2">
                <MessageSquare className="h-4 w-4" />
                备注信息
              </h3>
              <div className="bg-gray-50 p-3 rounded">
                <p className="text-sm leading-relaxed text-gray-700">{staff.remark}</p>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
            {onEdit && (
              <Button onClick={() => onEdit(staff)}>
                编辑员工
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 