"use client"

import {StaffListItem} from "@/api-real/staff"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {Loader2} from "lucide-react"


interface ConfirmDeleteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  staff: StaffListItem | null
  onConfirm: () => void
  isDeleting: boolean
}

export function ConfirmDeleteDialog({
  open,
  onOpenChange,
  staff,
  onConfirm,
  isDeleting,
}: ConfirmDeleteDialogProps) {
  if (!staff) return null; // 如果没有员工信息，不渲染对话框

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认离职</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要让员工 "<strong>{staff.name}</strong>" (UID: {staff.uid}) 离职吗？此操作无法撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={isDeleting} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                处理中...
              </>
            ) : (
              '确认离职'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
} 