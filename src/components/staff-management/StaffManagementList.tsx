"use client"

import {useCallback, useEffect, useState} from 'react'
import {StaffsTable} from './StaffManagementTable'

import {StaffsManagementDialog} from './StaffManagementDialog'
import {StaffDetailDialog} from './StaffDetailDialog'
import {StaffFilter} from './StaffFilter'

import {StaffFilters} from './StaffFilter'
import {toast} from 'sonner'
import {Loader2} from 'lucide-react'

import {ConfirmDeleteDialog} from './ConfirmDeleteDialog'
import {StaffApi, StaffListItem, Staff, CreateStaffRequest, UpdateStaffRequest} from '@/api-real/staff'


export function StaffManagementList() {
  const [staffs, setStaffs] = useState<StaffListItem[]>([])
  const [isLoading, setIsLoading] = useState(true) // 初始加载
  const [isSaving, setIsSaving] = useState(false) // 保存操作加载
  const [isDeleting, setIsDeleting] = useState(false) // 删除操作加载状态
  const [filters, setFilters] = useState<StaffFilters>({})

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize] = useState(10)

  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogTitle, setDialogTitle] = useState('')
  const [currentStaff, setCurrentStaff] = useState<Staff | undefined>(undefined)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false) // 删除确认对话框状态
  const [staffToDelete, setStaffToDelete] = useState<StaffListItem | null>(null) // 要删除的员工信息

  // 详情查看对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [detailStaff, setDetailStaff] = useState<Staff | undefined>(undefined)
  
  // 获取分页员工列表 - 使用真实API
  const fetchPaginatedStaffs = useCallback(async (page: number, filters: StaffFilters) => {
    setIsLoading(true)
    try {
      // 将筛选参数转换为API需要的格式
      const apiFilters = {
        department: filters.department,
        position: filters.position,
        region: filters.region,
        gender: filters.gender,
      }

      // 传递搜索和筛选参数到 API
      const response = await StaffApi.getStaffList(page, pageSize, filters.search, apiFilters)

      // 现在搜索和筛选都由后端处理，直接使用返回的数据
      setStaffs(response.list)
      setTotalPages(response.total_page)
    } catch (error) {
      console.error("获取员工列表错误:", error)
      toast.error("获取员工列表失败，请稍后重试")
      setStaffs([])
      setTotalPages(1)
    } finally {
      setIsLoading(false)
    }
  }, [pageSize])
  
  // 初始化加载和页面变化时加载数据
  useEffect(() => {
    fetchPaginatedStaffs(currentPage, filters)
  }, [currentPage, JSON.stringify(filters), fetchPaginatedStaffs])
  
  // 处理页面变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  // 打开新增客户对话框
  const handleAddClick = () => {
    setCurrentStaff(undefined)
    setDialogTitle('新增员工')
    setDialogOpen(true)
  }
  
  // 打开编辑员工对话框
  const handleEditClick = async (staff: StaffListItem) => {
    try {
      // 获取员工详细信息
      const staffDetail = await StaffApi.getStaffDetail(staff.uid)
      setCurrentStaff(staffDetail)
      setDialogTitle('编辑员工')
      setDialogOpen(true)
    } catch (error) {
      console.error("获取员工详情错误:", error)
      toast.error("获取员工详情失败，请稍后重试")
    }
  }

  // 打开员工详情查看对话框
  const handleViewClick = async (staff: StaffListItem) => {
    try {
      // 获取员工详细信息
      const staffDetail = await StaffApi.getStaffDetail(staff.uid)
      setDetailStaff(staffDetail)
      setDetailDialogOpen(true)
    } catch (error) {
      console.error("获取员工详情错误:", error)
      toast.error("获取员工详情失败，请稍后重试")
    }
  }

  // 从详情对话框打开编辑对话框
  const handleEditFromDetail = (staff: Staff) => {
    setDetailDialogOpen(false)
    setCurrentStaff(staff)
    setDialogTitle('编辑员工')
    setDialogOpen(true)
  }

  // 修改 handleDeleteClick 以打开确认对话框
  const handleDeleteClick = (staff: StaffListItem) => {
    setStaffToDelete(staff)
    setIsDeleteDialogOpen(true)
  }
  
  // 处理确认删除逻辑 - 使用真实API
  const handleConfirmDelete = async () => {
    if (!staffToDelete) return;

    setIsDeleting(true)
    try {
      await StaffApi.staffLeave(staffToDelete.uid)
      toast.success(`员工 "${staffToDelete.name}" 离职成功`)
      setIsDeleteDialogOpen(false)

      // 刷新列表，处理边界情况
      if (staffs.length === 1 && currentPage > 1) {
        setCurrentPage(currentPage - 1)
      } else {
        fetchPaginatedStaffs(currentPage, filters)
      }
    } catch (error) {
      console.error("员工离职错误:", error)
      toast.error("员工离职操作失败，请稍后重试")
      setIsDeleteDialogOpen(false)
    } finally {
      setIsDeleting(false)
      setStaffToDelete(null)
    }
  }
  
  // 处理保存员工 - 使用真实API
  const handleSaveStaff = async (staffData: CreateStaffRequest | (UpdateStaffRequest & { uid?: string })) => {
    setIsSaving(true)
    try {
      if ('uid' in staffData && staffData.uid) {
        // 编辑现有员工
        const updateData: UpdateStaffRequest = {
          name: staffData.name,
          gender: staffData.gender,
          phone: staffData.phone,
          email: staffData.email,
          department: staffData.department,
          position: staffData.position,
          region: staffData.region,
          join_date: staffData.join_date,
          contract_end_date: staffData.contract_end_date,
          remark: staffData.remark,
        }

        await StaffApi.updateStaff(staffData.uid, updateData)
        toast.success(`员工 "${staffData.name}" 更新成功`)
        setDialogOpen(false)
        fetchPaginatedStaffs(currentPage, filters)
      } else {
        // 新增员工
        const createData: CreateStaffRequest = {
          name: staffData.name,
          gender: staffData.gender,
          phone: staffData.phone,
          email: staffData.email,
          department: staffData.department,
          position: staffData.position,
          region: staffData.region,
          join_date: staffData.join_date,
          contract_end_date: staffData.contract_end_date,
          remark: staffData.remark,
        }

        await StaffApi.createStaff(createData)
        toast.success(`员工 "${staffData.name}" 创建成功`)
        setDialogOpen(false)

        // 刷新列表到第一页
        if (currentPage === 1) {
          fetchPaginatedStaffs(1, filters)
        } else {
          setCurrentPage(1)
        }
      }
    } catch (error) {
      console.error("保存员工错误:", error)
      toast.error("保存员工时发生错误，请稍后重试")
    } finally {
      setIsSaving(false)
    }
  }
  
  // 处理过滤器变化
  const handleFiltersChange = useCallback((newFilters: StaffFilters) => {
    setFilters(newFilters)
    setCurrentPage(1) // 重置到第一页
  }, [])

  // 显示全页面加载状态
  // 注意：只在没有搜索词且首次加载时显示全屏加载
  // 如果是带搜索词的加载或者翻页加载，使用表格内的加载状态
  if (isLoading && staffs.length === 0 && !filters.search) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">正在加载员工数据...</p>
        </div>
      </div>
    )
  }

  return (
    <>
        <StaffFilter
          onFiltersChange={handleFiltersChange}
          onAddClick={handleAddClick}
          className="mb-4"
        />
        
        <StaffsTable 
          staffs={staffs} 
          isLoading={isLoading && staffs.length === 0} 
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onView={handleViewClick}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
        
        <StaffsManagementDialog 
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          staff={currentStaff}
          onSave={handleSaveStaff}
          title={dialogTitle}
          isSaving={isSaving}
        />

        <ConfirmDeleteDialog 
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          staff={staffToDelete}
          onConfirm={handleConfirmDelete}
          isDeleting={isDeleting}
        />

        <StaffDetailDialog
          open={detailDialogOpen}
          onOpenChange={setDetailDialogOpen}
          staff={detailStaff}
          onEdit={handleEditFromDetail}
        />
    </>
  )
} 