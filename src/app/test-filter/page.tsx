"use client"

import { SimpleFilterTest } from '@/components/common/SimpleFilterTest'
import { DatePickerTest } from '@/components/common/DatePickerTest'
import { EnhancedFilterExample } from '@/components/common/EnhancedFilterExample'
import { UniversalFilterExample } from '@/components/common/UniversalFilterExample'

export default function TestFilterPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8 space-y-12">
        {/* 简单测试 */}
        <SimpleFilterTest />

        {/* 日期选择器测试 */}
        <div className="border-t pt-8">
          <DatePickerTest />
        </div>

        {/* 增强版示例 */}
        <div className="border-t pt-8">
          <EnhancedFilterExample />
        </div>

        {/* 原始示例 */}
        <div className="border-t pt-8">
          <UniversalFilterExample />
        </div>
      </div>
    </div>
  )
}
