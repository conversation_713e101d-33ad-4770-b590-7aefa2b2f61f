/**
 * 员工管理相关常量定义
 * 基于API文档中的枚举值
 */

// 性别枚举
export const GENDER = {
  MALE: 'MALE',
  FEMALE: 'FEMALE',
} as const;

export type Gender = typeof GENDER[keyof typeof GENDER];

// 性别显示映射
export const GENDER_DISPLAY_MAP = {
  [GENDER.MALE]: '男',
  [GENDER.FEMALE]: '女',
} as const;

// 部门枚举 (基于后端实际枚举)
export const DEPARTMENT = {
  DESIGN_TEAM: 'DESIGN_TEAM',
  ENGINEERING_TEAM: 'ENGINEERING_TEAM',
  AFTER_SALES_TEAM: 'AFTER_SALES_TEAM',
  SALES_TEAM: 'SALES_TEAM',
  OPERATION_TEAM: 'OPERATION_TEAM',
} as const;

export type Department = typeof DEPARTMENT[keyof typeof DEPARTMENT];

// 部门显示映射
export const DEPARTMENT_DISPLAY_MAP = {
  [DEPARTMENT.DESIGN_TEAM]: '设计团队',
  [DEPARTMENT.ENGINEERING_TEAM]: '工程团队',
  [DEPARTMENT.AFTER_SALES_TEAM]: '售后团队',
  [DEPARTMENT.SALES_TEAM]: '销售团队',
  [DEPARTMENT.OPERATION_TEAM]: '运营团队',
} as const;

// 职位枚举 (基于后端实际枚举)
export const POSITION = {
  DESIGN_LEADER: 'DESIGN_LEADER',
  DESIGNER: 'DESIGNER',
  ENGINEERING_LEADER: 'ENGINEERING_LEADER',
  ENGINEER: 'ENGINEER',
  AFTER_SALES_LEADER: 'AFTER_SALES_LEADER',
  AFTER_SALES_ENGINEER: 'AFTER_SALES_ENGINEER',
  SALES_LEADER: 'SALES_LEADER',
  Sales_SUPPORT: 'Sales_SUPPORT',
  SALES: 'SALES',
  PRODUCT_MANAGER: 'PRODUCT_MANAGER',
  OPERATION_SPECIALIST: 'OPERATION_SPECIALIST',
} as const;

export type Position = typeof POSITION[keyof typeof POSITION];

// 职位显示映射
export const POSITION_DISPLAY_MAP = {
  [POSITION.DESIGN_LEADER]: '设计主管',
  [POSITION.DESIGNER]: '设计师',
  [POSITION.ENGINEERING_LEADER]: '工程主管',
  [POSITION.ENGINEER]: '技术员',
  [POSITION.AFTER_SALES_LEADER]: '售后主管',
  [POSITION.AFTER_SALES_ENGINEER]: '售后技术员',
  [POSITION.SALES_LEADER]: '销售主管',
  [POSITION.Sales_SUPPORT]: '销售支持',
  [POSITION.SALES]: '销售',
  [POSITION.PRODUCT_MANAGER]: '产品经理',
  [POSITION.OPERATION_SPECIALIST]: '运营专员',
} as const;

// 区域枚举 (基于后端实际枚举)
export const REGION = {
  NORTH_PRODUCT_CENTER_MATRIX: 'NORTH_PRODUCT_CENTER_MATRIX',
  SOUTH_PRODUCT_CENTER_MATRIX: 'SOUTH_PRODUCT_CENTER_MATRIX',
  CHINA_PRODUCT_CENTER_MATRIX: 'CHINA_PRODUCT_CENTER_MATRIX',
  DANANG_PRODUCT_CENTER_MATRIX: 'DANANG_PRODUCT_CENTER_MATRIX',
  HANOI_CENTER_MATRIX: 'HANOI_CENTER_MATRIX',
} as const;

export type Region = typeof REGION[keyof typeof REGION];

// 区域显示映射
export const REGION_DISPLAY_MAP = {
  [REGION.NORTH_PRODUCT_CENTER_MATRIX]: '北部产品中心矩阵',
  [REGION.SOUTH_PRODUCT_CENTER_MATRIX]: '南部产品中心矩阵',
  [REGION.CHINA_PRODUCT_CENTER_MATRIX]: '中资产品中心矩阵',
  [REGION.DANANG_PRODUCT_CENTER_MATRIX]: '岘港产品中心矩阵',
  [REGION.HANOI_CENTER_MATRIX]: '河内中心矩阵',
} as const;

// 部门选项列表 (用于下拉选择)
export const DEPARTMENT_OPTIONS = [
  { value: DEPARTMENT.DESIGN_TEAM, label: DEPARTMENT_DISPLAY_MAP[DEPARTMENT.DESIGN_TEAM] },
  { value: DEPARTMENT.ENGINEERING_TEAM, label: DEPARTMENT_DISPLAY_MAP[DEPARTMENT.ENGINEERING_TEAM] },
  { value: DEPARTMENT.AFTER_SALES_TEAM, label: DEPARTMENT_DISPLAY_MAP[DEPARTMENT.AFTER_SALES_TEAM] },
  { value: DEPARTMENT.SALES_TEAM, label: DEPARTMENT_DISPLAY_MAP[DEPARTMENT.SALES_TEAM] },
  { value: DEPARTMENT.OPERATION_TEAM, label: DEPARTMENT_DISPLAY_MAP[DEPARTMENT.OPERATION_TEAM] },
] as const;

// 职位选项列表 (用于下拉选择)
export const POSITION_OPTIONS = [
  { value: POSITION.DESIGN_LEADER, label: POSITION_DISPLAY_MAP[POSITION.DESIGN_LEADER] },
  { value: POSITION.DESIGNER, label: POSITION_DISPLAY_MAP[POSITION.DESIGNER] },
  { value: POSITION.ENGINEERING_LEADER, label: POSITION_DISPLAY_MAP[POSITION.ENGINEERING_LEADER] },
  { value: POSITION.ENGINEER, label: POSITION_DISPLAY_MAP[POSITION.ENGINEER] },
  { value: POSITION.AFTER_SALES_LEADER, label: POSITION_DISPLAY_MAP[POSITION.AFTER_SALES_LEADER] },
  { value: POSITION.AFTER_SALES_ENGINEER, label: POSITION_DISPLAY_MAP[POSITION.AFTER_SALES_ENGINEER] },
  { value: POSITION.SALES_LEADER, label: POSITION_DISPLAY_MAP[POSITION.SALES_LEADER] },
  { value: POSITION.Sales_SUPPORT, label: POSITION_DISPLAY_MAP[POSITION.Sales_SUPPORT] },
  { value: POSITION.SALES, label: POSITION_DISPLAY_MAP[POSITION.SALES] },
  { value: POSITION.PRODUCT_MANAGER, label: POSITION_DISPLAY_MAP[POSITION.PRODUCT_MANAGER] },
  { value: POSITION.OPERATION_SPECIALIST, label: POSITION_DISPLAY_MAP[POSITION.OPERATION_SPECIALIST] },
] as const;

// 区域选项列表 (用于下拉选择)
export const REGION_OPTIONS = [
  { value: REGION.NORTH_PRODUCT_CENTER_MATRIX, label: REGION_DISPLAY_MAP[REGION.NORTH_PRODUCT_CENTER_MATRIX] },
  { value: REGION.SOUTH_PRODUCT_CENTER_MATRIX, label: REGION_DISPLAY_MAP[REGION.SOUTH_PRODUCT_CENTER_MATRIX] },
  { value: REGION.CHINA_PRODUCT_CENTER_MATRIX, label: REGION_DISPLAY_MAP[REGION.CHINA_PRODUCT_CENTER_MATRIX] },
  { value: REGION.DANANG_PRODUCT_CENTER_MATRIX, label: REGION_DISPLAY_MAP[REGION.DANANG_PRODUCT_CENTER_MATRIX] },
  { value: REGION.HANOI_CENTER_MATRIX, label: REGION_DISPLAY_MAP[REGION.HANOI_CENTER_MATRIX] },
] as const;

// 性别选项列表 (用于下拉选择)
export const GENDER_OPTIONS = [
  { value: GENDER.MALE, label: GENDER_DISPLAY_MAP[GENDER.MALE] },
  { value: GENDER.FEMALE, label: GENDER_DISPLAY_MAP[GENDER.FEMALE] },
] as const;
